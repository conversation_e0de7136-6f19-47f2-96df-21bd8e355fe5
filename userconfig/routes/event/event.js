


module.exports = {
    /**
     *
     * @api {get} /api/events Events List
     * @apiDescription Returns events list
     * @apiGroup Event
     *
     */
    'get /api/events': 'EventController.index',

    /**
     *
     * @api {get} /api/seasons Event Seasons
     * @apiDescription Returns event seasons
     * @apiGroup Event
     *
     */
    'get /api/seasons': 'EventController.getSeasons',

    /**
     *
     * @api {delete} /api/event/:event Event Removing
     * @apiDescription Removes a specific event
     * @apiGroup Event
     *
     */
    'delete /api/event/:event': 'EventController.destroy',
    /*'get /api/events/club/:club': 'EventController.index',*/

    /**
     *
     * @api {get} /api/event/:event Event Settings
     * @apiDescription Returns a specific event settings
     * @apiGroup Event
     *
     */
    'get /api/event/:event': 'EventController.find',

    /**
     *
     * @api {get} /api/event/:event/info Event Info
     * @apiDescription Returns a specific event info
     * @apiGroup Event
     *
     */
    'get /api/event/:event/info': 'EventController.info',
    // 'post /api/event': 'EventController.create',
    // 'put /api/event/:event': 'EventController.update',

    /**
     *
     * @api {get} /api/event/:event/history Event History
     * @apiDescription Returns a specific event history data
     * @apiGroup Event
     *
     */
    'get /api/event/:event/history': 'EventController.historyNew',

    /**
     *
     * @api {get} /api/event/:event/history/:email Event Emails
     * @apiDescription Returns all event's contact emails
     * @apiGroup Event
     *
     */
    'get /api/event/:event/history/:email': 'EventController.get_mail_info',

    /**
     *
     * @api {get} /api/event/:event/email/:email/preview Event Email Preview
     * @apiDescription Returns sent event's email preview
     * @apiGroup Event
     *
     */
    'get /api/event/:event/email/:email/preview': 'EventController.getEmailHtml',

    /**
     *
     * @api {post} /api/event_note Event Save Note
     * @apiDescription Saves event note
     * @apiGroup Event
     *
     */
    'post /api/event_note': 'EventController.save_note',

    /**
     *
     * @api {get} /api/event/:event/genders Genders List
     * @apiDescription Returns event genders list
     * @apiGroup Event
     *
     */
    'get /api/event/:event/genders': 'EventController.getGenders',

    /**
     *
     * @api {get} /api/event/:event/acl Event ACL
     * @apiDescription Returns a specific event ACL data
     * @apiGroup Event
     *
     */
    'get /api/event/:event/acl': 'EventController.getEventUserAcl',
}
