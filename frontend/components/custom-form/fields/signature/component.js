
class Controller {
    constructor ($timeout, scope, signaturePadService) {
        this.$timeout = $timeout;
        this.scope = scope;
        // Use injected factory from 'signaturePad' Angular module instead of relying on a global
        this.SignaturePad = signaturePadService;
    }
    $onInit () {
        this.$timeout(() => {
            this.canvas = document.getElementById(this.getID());
        }).then(() => {
            this.signaturePad = new this.SignaturePad(this.canvas, {
                backgroundColor: 'rgb(255, 255, 255)'
            });

            this.signaturePad.addEventListener("endStroke", () => {
                if (!this.signaturePad.isEmpty()) {
                    this.scope.$apply(() => {
                        this.value = this.signaturePad.toDataURL('image/svg+xml');
                    });
                }
            });
        })

        this.scope.$on('$destroy', () => this.signaturePad.off());
    }

    getID  () {
        return 'signature-pad-' + this.field.id;
    }

    clear () {
        this.signaturePad.clear();
        this.value = null;
    }
}

Controller.$inject = ['$timeout', '$scope', 'signaturePadService'];

angular.module('SportWrench').component('customFormFieldSignature', {
    templateUrl: 'components/custom-form/fields/signature/template.html',
    bindings: {
        value: '=',
        field: '<',
        fieldHasError: '&'
    },
    controller: Controller
});

