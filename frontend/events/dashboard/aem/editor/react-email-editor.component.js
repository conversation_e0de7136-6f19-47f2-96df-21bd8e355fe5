angular.module('SportWrench').component('reactEmailEditor', {
	templateUrl: 'events/dashboard/aem/editor/react-email-editor.html',
	bindings: {
		templateId: '<tmplId',
		onSave: '&',
		onSend: '&',
		beeJSON: '<beeTmpl',
		templateJSON: '<tmpl',
		templateHTML: '<tmplHtml',
		mergeTags: '<?',
		onLoaded: '&?onLoaded',
		editorWindowHeight: '<?',
		onAutoSave: '&',
	},
	controller: ['ReactEmailEditor', '$element', '$timeout', 'emailEditorImageService', ReactEmailEditorController],
});

function ReactEmailEditorController(ReactEmailEditor, $element, $timeout, emailEditorImageService) {
	const self = this;

	this.containerID = 'react-email-editor-' + Date.now();

	this.reactEmailEditorInstance = null;

	this.saveTemplate = function (json, html) {
		this.onSave({ json, html });
	};

	// jsonFile
	this.saveFileToLocalStorage = function (json) {
		this.onAutoSave({ json });
	};

	this.sendTemplate = function (html) {
		this.onSend({ html, reactEmailEditorInstance: self.reactEmailEditorInstance });
	};

	this.logError = function (errorMessage) {
		console.log('error', errorMessage);
	};

	this.$onChanges = function (changes) {
		let parentElementHeight =
			changes.editorWindowHeight && changes.editorWindowHeight.currentValue;
		if (angular.isNumber(parentElementHeight)) {
			let editorParent = $element.children()[0];
			angular.element(editorParent).css('height', parentElementHeight + 'px');
		}
	};

	var editorConfig = {
        projectId: 116465,
        containerId: this.containerID,
        beeJSON: this.beeJSON,
        templateJSON: this.templateJSON,
        templateHTML: this.templateHTML,
        // call onSave after onSend
        preventClose: true,
        onSave: this.saveTemplate.bind(this),
        onAutoSave: this.saveFileToLocalStorage.bind(this),
        onSend: this.sendTemplate.bind(this),
        onError: this.logError.bind(this),
        mergeTags: this.mergeTags || [],
        unlayerOptions: {
            tools: {
                custom_media: {
                    enabled: true,
                },
            },
			mergeTags: this.mergeTags || [],
        },
		fetchUploads: () => emailEditorImageService.getEOImages(),
		onImageUpload: (file) => emailEditorImageService.create(file),
		onImageDelete: (id) => emailEditorImageService.delete(id)
    };

	// https://stackoverflow.com/questions/23620665/dynamically-generated-ids-null-targets
	// https://stackoverflow.com/questions/28560918/dynamic-id-inside-angularjs-template
	$timeout(async () => {
		this.reactEmailEditorInstance =
			await ReactEmailEditor.getPlugin().renderTemplate(editorConfig);

		if (this.onLoaded) {
			let editorContainer = $element.find('#' + this.containerID);

			this.onLoaded({
				data: {
					height: editorContainer.height(),
				},
			});
		}
	});
}
