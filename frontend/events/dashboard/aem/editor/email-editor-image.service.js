angular
    .module('SportWrench')
    .factory('emailEditorImageService', function ($http) {
        return {
            getEOImages: function (callBack) {
                return $http
                    .get('/api/email-editor-images/v2')
                    .then(({ data }) => data);
            },
            create: function (file) {
                const formData = new FormData();
                console.log(file)
                formData.append('file', file);

                return $http.post('/api/email-editor-images/v2', formData, {
                    headers: { 'Content-Type': undefined },
                    withCredentials: true,
                    transformRequest: angular.identity
                });
            },
            delete: function (imageId) {
                return $http
                    .delete(`/api/email-editor-images/v2/${imageId}`);
            },
        };
    });
