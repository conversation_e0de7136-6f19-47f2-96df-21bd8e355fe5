<div class="container no-animate">
    <div class="row">
        <div class="col-sm-10 col-sm-offset-1">
            <h2 class="text-info text-center">
                {{::utils.title}} an Event<br/>
                <a href="" ng-click="openDashboard()" class="text-grey" ng-if="isUpdateMode()">
                    <small ng-bind="settings.tournament.long_name" class="text-grey"></small>
                </a>
            </h2>
            <uib-tabset active="settings.activeTabIndex"><!-- justified="isUpdateMode()" -->
                <uib-tab index="settings.tabs.general.index" ng-if="showGeneralTab()">
                    <uib-tab-heading><i ng-class="{'fa fa-cog': true, 'fa-spin': settings.tabs.general.isEventLoading }"></i> {{::settings.tabs.general.title}}</uib-tab-heading>
                    <general-settings></general-settings>
                </uib-tab>
                <!--tab active="settings.tabs.divisions.active" ng-if="isUpdateMode()">
                    <tab-heading><i class="fa fa-users"></i> {{::settings.tabs.divisions.title}}</tab-heading>
                    <divisions-settings></divisions-settings>
                </tab-->
                <uib-tab index="settings.tabs.locations.index" ng-if="showLocationsTab()">
                    <uib-tab-heading><i ng-class="{ 'fa fa-university': true, 'faa-pulse animated': settings.tabs.locations.isLoading }"></i> {{::settings.tabs.locations.title}}</uib-tab-heading>
                    <locations-settings></locations-settings>
                </uib-tab>
                <!--tab active="settings.tabs.booths.active" ng-if="isUpdateMode()">
                    <tab-heading><i class="fa fa-hdd-o"></i> {{::settings.tabs.booths.title}}</tab-heading>
                    <booths-settings></booths-settings>
                </uib-tab>
                <uib-tab active="settings.tabs.tickets.active" ng-if="isUpdateMode()">
                    <tab-heading><i class="fa fa-ticket"></i> {{::settings.tabs.tickets.title}}</tab-heading>
                    <tickets-settings></tickets-settings>
                </ta-->
                <uib-tab index="settings.tabs.eventUsers.index" ng-if="showEventUsers() && isUpdateMode()">
                    <uib-tab-heading><i ng-class="{'fa fa-user': true }"></i> {{::settings.tabs.eventUsers.title}}</uib-tab-heading>
                    <event-users></event-users>
                </uib-tab>
                <uib-tab index="settings.tabs.assignTemplates.index" ng-if="showTransactionalEmailsTab()">
                    <uib-tab-heading><i ng-class="{'fa fa-newspaper-o': true }"></i> {{::settings.tabs.assignTemplates.title}}</uib-tab-heading>
                    <assign-template></assign-template>
                </uib-tab>
            </uib-tabset>
        </div>
    </div>
</div>
