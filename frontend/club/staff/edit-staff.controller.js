angular.module('SportWrench').controller('EditStaffController', EditStaffController);

EditStaffController.$inject = [
    '$scope', 'staffService', 'ClubTeamsService', '$state', '$stateParams', '$uibModalInstance', '$rootScope',
    'INTERNAL_ERROR_MSG', 'toastr', 'INVALID_FORM_ERROR_MSG', 'DateService', 'moment', 'SANCTIONING_BODY', 'loadClub', 'masterClubService'
];

function EditStaffController (
    $scope, staffService, ClubTeamsService, $state, $stateParams, $uibModalInstance, $rootScope,
    INTERNAL_ERROR_MSG, toastr, INVALID_FORM_ERROR_MSG, DateService, moment, SANCTIONING_BODY, loadClub, masterClubService
) {
    var master_staff_id = $stateParams.master_staff_id;
    $scope.states = [];
    $scope.staff = {};
    $scope.teams = [];
    $scope.roles = [
        {id: 0, name: '(Title not set...)'}
    ];
    $scope.data_loaded = false;
    $scope.loadClub = loadClub;

    $scope.utils = {
        changesMessage: '',
        action: ''
    }

    $scope.roleChanges = {
        teamId: 0,
        action: '',
        roleId: 0
    }

    $scope.data = {};

    $scope.blockedEvents = [];

    _load(function (response) {
        var data = response.data;
        $scope.data_loaded      = true;
        $scope.states           = data.states;
        $scope.staff            = data.staff;
        $scope.teams            = data.teams;
        $scope.roles            = $scope.roles.concat(data.roles);
        $scope.blockedEvents    = data.blocked_events;
        $scope.staff.birthdate  = DateService.normalizeStr($scope.staff.birthdate, true);
    });

    function _load (cb) {
        staffService.getClubStaffById(master_staff_id, function(data) {
            if(cb) return cb(data);
        });
    }

    $scope.roleChange = function (team, action, prevRoleId) {
        var teamId = team.id;

        if(action === 'update' && team.role_id === team.initial_role_id) {
            return;
        }

        if(!__roleChangeConfirm(action)) {
            if(action === 'update') {
                team.role_id = prevRoleId;
            }
            return;
        }

        staffService.changeRoleRows(master_staff_id, teamId, {
            role_id: team.role_id
        }, action)
        .success(function () {
            $scope.roleChanges.teamId = 0;
            $scope.roleChanges.roleId = 0;
            _load(function (response) {
                $scope.teams = response.data.teams;
                $rootScope.$broadcast('club.staff.reloadList');
            });
        }).error(() => {
            if(action === 'update') {
                team.role_id = prevRoleId;
            }
        })
    }

    function __roleChangeConfirm (action) {
        var message = 'Are you sure you want to ';
        switch(action) {
            case 'primary':
                message += 'set primary for the staff in this team';
                break;
            case 'remove':
                message += 'remove the staff form this team';
                break;
            default:
                message += 'change role for the staff';
                break;
        }
        message += '?';
        return confirm(message);
    }

    $scope.add_to_team = function (team_id) {
        staffService.addStaffToTeam(team_id, [master_staff_id], true).success(function () {
            _load(function (data) {
                $scope.teams = data.data.teams;
                $rootScope.$broadcast('club.staff.reloadList');
            });
        }).error(function () {
            toastr.error('Failed');
        });
    };

    $scope.updateStaff = function() {
        if($scope.data.staffForm.$invalid) {
            toastr.warning(INVALID_FORM_ERROR_MSG);
            return;
        }

        staffService.updateStaff(
            master_staff_id,
            _.omit($scope.staff, 'membership_status', 'organization_code', 'last', 'cert', 'chaperone_status',
                'coach_status', 'bg_screening', 'bg_expire_date', 'gender', 'usav_number',
                'aau_membership_id', 'birthdate'),
            function () {
                $rootScope.$broadcast('club.staff.reloadList');
                $uibModalInstance.close();
                toastr.success('Staff information has been successfully edited');
            }
        );
    };

    $uibModalInstance.result.finally(function() {
        $state.go('^');
    });

    $scope.hasAauSanctioning = function () {
        return $scope.loadClub && $scope.loadClub.sport_sanctionings
            && $scope.loadClub.sport_sanctionings.includes(SANCTIONING_BODY.AAU);
    };

    $scope.hasUsavSanctioning = function () {
        return $scope.loadClub && $scope.loadClub.sport_sanctionings
            && $scope.loadClub.sport_sanctionings.includes(SANCTIONING_BODY.USAV);
    };

    $scope.getUsavSafesportStatus = function(staff) {
        if (!$scope.hasUsavSanctioning() || !staff.usav_number) return '-';
        return staff.safesport_statusid || 'NO';
    };

    $scope.getUsavBkgStatus = function(staff) {
        if (!$scope.hasUsavSanctioning() || !staff.usav_number) return '-';
        return staff.bg_screening || 'NO';
    };

    $scope.getAauSafesportStatus = function(staff) {
        return $scope.hasAauSanctioning() && staff.aau_membership_id ? 'OK' : '-';
    };

    $scope.getAauBkgStatus = function(staff) {
        return $scope.hasAauSanctioning() && staff.aau_membership_id ? 'YES' : '-';
    };
}
