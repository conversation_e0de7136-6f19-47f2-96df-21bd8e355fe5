<div class="modal-header">
    <h2>Staff: {{staff.first}} {{staff.last}}</h2>
</div>
<div class="modal-body">
    <uib-tabset>
        <uib-tab heading="Edit Staff">
            <form class="form-horizontal row-space" ng-submit="updateStaff()" name="data.staffForm">
                <div class="{{$root.getControlClass(data.staffForm, 'first', true)}}">
                    <label for="first" class="col-sm-4 control-label">First</label>
                    <div class="col-sm-7">
                        <input ng-model="staff.first" name="first" type="text" class="form-control" placeholder="First" required>
                    </div>
                </div>
                <div class="{{$root.getControlClass(data.staffForm, 'last', true)}}">
                    <label for="last" class="col-sm-4 control-label">Last</label>
                    <div class="col-sm-7">
                        <input ng-model="staff.last" name="last" type="text" disabled class="form-control" placeholder="Last" required >
                    </div>
                </div>
                <div class="form-group">
                    <label for="nick" class="col-sm-4 control-label">Nick</label>
                    <div class="col-sm-7">
                        <input ng-model="staff.nick" name="nick" type="text" class="form-control" placeholder="Nick" >
                    </div>
                </div>
                <div class="form-group">
                    <label for="gender" class="col-sm-4 control-label">Gender</label>
                    <div class="col-sm-7">
                       <label class="control-label capitalize" ng-bind="staff.gender ? staff.gender : ''"></label>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-4 control-label">Sanctioned body</label>
                    <div class="col-sm-7">
                        <label class="form-check-label" ng-if="staff.usav_number">USAV</label>
                        <label class="form-check-label" ng-if="staff.aau_membership_id">AAU</label>
                    </div>
                </div>
                <div class="form-group">
                    <label for="birthdate" class="col-sm-4 control-label">Birthdate</label>
                    <div class="col-sm-7">
                        <div class="birthdate-disabled" style="pointer-events: none;">
                            <date-time-form-control
                                date="staff.birthdate"
                                format="MM/dd/yyyy"
                                timepicker="false"
                                name="birthdate"
                            >
                            </date-time-form-control>
                        </div>
                    </div>
                </div>

                <div class="{{$root.getControlClass(data.staffForm, 'phoneh')}}">
                    <label for="phone" class="col-sm-4 control-label">Home Phone</label>
                    <div class="col-sm-7">
                        <input ng-model="staff.phoneh" name="phoneh" type="tel" ui-mask="(*************" phone-validator class="form-control">
                    </div>
                </div>
                <div class="{{$root.getControlClass(data.staffForm, 'phone')}}">
                    <label for="phone" class="col-sm-4 control-label">Mobile Phone</label>
                    <div class="col-sm-7">
                        <input ng-model="staff.phone" name="phone" type="tel" ui-mask="(*************" phone-validator class="form-control">
                    </div>
                </div>
                <div class="{{$root.getControlClass(data.staffForm, 'phoneo')}}">
                    <label for="phoneo" class="col-sm-4 control-label">Other Phone</label>
                    <div class="col-sm-7">
                        <input ng-model="staff.phoneo" name="phoneo" type="tel" ui-mask="(*************" phone-validator class="form-control">
                    </div>
                </div>
                <div class="{{$root.getControlClass(data.staffForm, 'phonew')}}">
                    <label for="phoneo" class="col-sm-4 control-label">Work Phone</label>
                    <div class="col-sm-7">
                        <input ng-model="staff.phonew" name="phonew" type="tel" ui-mask="(*************" phone-validator  class="form-control">
                    </div>
                </div>

                <div class="form-group">
                    <label for="email" class="col-sm-4 control-label">Email</label>
                    <div class="col-sm-7">
                        <input ng-model="staff.email" type="email" class="form-control" email-validator placeholder="Email" >
                    </div>
                </div>
                <div class="form-group">
                    <label for="zip" class="col-sm-4 control-label">ZIP</label>
                    <div class="col-sm-7">
                        <input ng-model="staff.zip" type="text" class="form-control" placeholder="ZIP" maxlength="20" ng-pattern="/^[a-zA-Z0-9]+$/">
                    </div>
                </div>
                <div class="form-group">
                    <label for="address" class="col-sm-4 control-label">Address</label>
                    <div class="col-sm-7">
                        <input ng-model="staff.address" name="address" type="text" class="form-control" placeholder="Address" >
                    </div>
                </div>
                <div class="form-group">
                    <label for="address2" class="col-sm-4 control-label">Address 2</label>
                    <div class="col-sm-7">
                        <input ng-model="staff.address2" name="address2" type="text" class="form-control" placeholder="Address 2">
                    </div>
                </div>
                <div class="form-group">
                    <label for="city" class="col-sm-4 control-label">City</label>
                    <div class="col-sm-7">
                        <input ng-model="staff.city" type="text" class="form-control" placeholder="City">
                    </div>
                </div>
                <div class="form-group">
                    <label for="state" class="col-sm-4 control-label">State</label>
                    <div class="col-sm-7">
                        <select class="form-control"
                            ng-options="st.state as st.name for st in states | orderBy:'name'"
                            ng-model="staff.state"
                            name="state"
                            >
                            <option value="" selected>Select...</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label for="code" class="col-sm-4 control-label">USAV Code</label>
                    <div class="col-sm-7">
                        <input ng-model="staff.organization_code" disabled type="text" class="form-control" placeholder="USAV Code">
                    </div>
                </div>
                <div class="form-group" ng-if="hasAauSanctioning()">
                    <label for="aau_code" class="col-sm-4 control-label">AAU Code</label>
                    <div class="col-sm-7">
                        <input ng-model="staff.aau_membership_id" disabled type="text" class="form-control" placeholder="AAU Code">
                    </div>
                </div>
                <div class="form-group">
                    <label for="membership_status" class="col-sm-4 control-label">Membership Status</label>
                    <div class="col-sm-7">
                        <input ng-model="staff.membership_status" disabled name="membership_status" type="text" class="form-control" placeholder="Membership status">
                    </div>
                </div>
                <div class="form-group">
                    <label for="safesport_usav" class="col-sm-4 control-label">SafeSport (USAV)</label>
                    <div class="col-sm-7">
                        <input ng-value="getUsavSafesportStatus(staff)" disabled name="safesport_usav" type="text" class="form-control" placeholder="SafeSport">
                    </div>
                </div>
                <div class="form-group">
                    <label for="bg_usav" class="col-sm-4 control-label">BKG (USAV)</label>
                    <div class="col-sm-7">
                        <input ng-value="getUsavBkgStatus(staff)" disabled name="bg_usav" type="text" class="form-control" placeholder="Background Screening">
                    </div>
                </div>
                <div class="form-group">
                    <label for="safesport_aau" class="col-sm-4 control-label">SafeSport (AAU)</label>
                    <div class="col-sm-7">
                        <input ng-value="getAauSafesportStatus(staff)" disabled name="safesport_aau" type="text" class="form-control" placeholder="SafeSport">
                    </div>
                </div>
                <div class="form-group">
                    <label for="bg_aau" class="col-sm-4 control-label">BKG (AAU)</label>
                    <div class="col-sm-7">
                        <input ng-value="getAauBkgStatus(staff)" disabled name="bg_aau" type="text" class="form-control" placeholder="Background Screening">
                    </div>
                </div>
                <div class="form-group" ng-if="false"> <!--/SW-2123/-->
                    <label for="bg_expire_date" class="col-sm-4 control-label">BG Exp Date</label>
                    <div class="col-sm-7">
                        <input type="text" disabled ng-value="staff.bg_expire_date | date: 'MM/dd/yyyy hh:mm a'" name="bg_expire_date" class="form-control" >
                    </div>
                </div>
                <div class="form-group">
                    <label for="chaperone_status" class="col-sm-4 control-label">Chaperone Status</label>
                    <div class="col-sm-7 center-form-text">
                        <strong>{{staff.chaperone_status || 'N/A'}}</strong>
                    </div>
                </div>
                <div class="form-group">
                    <label for="coach_status" class="col-sm-4 control-label">Coach Status</label>
                    <div class="col-sm-7 center-form-text">
                        <strong>{{staff.coach_status || 'N/A'}}</strong>
                    </div>
                </div>
                <div class="form-group">
                    <label for="cert" class="col-sm-4 control-label">Cert</label>
                    <div class="col-sm-7 center-form-text">
                        <strong>{{staff.cert || 'N/A'}}</strong>
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-sm-offset-4 col-sm-7">
                      <button type="submit" class="btn btn-primary">Submit</button>
                    </div>
                </div>
            </form>
        </uib-tab>
        <uib-tab heading="Teams">
            <div class="row rowm0 row-space">
                <div class="col-sm-4">
                    <teams-dropdown
                        title="Add to Team"
                        btn-class="btn btn-default"
                        on-select="add_to_team(id)"
                    ></teams-dropdown>
                </div>
            </div>
            <blocked-events-list events="blockedEvents"></blocked-events-list>
          <table class="table table-condensed" ng-if="data_loaded && teams.length">
              <thead>
                  <tr>
                      <th>Team name</th>
                      <th>Title</th>
                      <th></th>
                      <th>Primary</th>
                  </tr>
              </thead>
              <tbody>
                  <tr ng-repeat="t in teams">
                      <td>{{t.team_name}}</td>
                      <td>
                          <select
                            ng-options="r.id as r.name for r in roles"
                            ng-model="t.role_id"
                            class="form-control form-control-select-small--162"
                            ng-change="roleChange(t, 'update', {{t.role_id}})"
                        ></select>
                      </td>
                      <td>
                          <button
                            class="btn btn-xs btn-danger"
                            ng-click="roleChange(t, 'remove')"
                            ><i class="fa fa-times"></i> Remove</button>
                      </td>
                      <td>
                         <button
                            class="btn btn-xs btn-default"
                            ng-click="roleChange(t, 'primary')"
                            ng-if="!t.primary">Set
                         </button>
                          <span class="glyphicon glyphicon-ok spacer-lg-l" ng-if="t.primary"></span>
                      </td>
                  </tr>
              </tbody>
          </table>
        </uib-tab>
    </uib-tabset>
</div>
<div class="modal-footer">
    <button class="btn btn-default" ng-click="$dismiss()">Close</button>
</div>
