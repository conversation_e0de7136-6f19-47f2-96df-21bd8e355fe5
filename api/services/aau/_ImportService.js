const ImportProcessService = require("../aau/members-import/_ImportProcessService");
const {WHITE_SPACE_REGEX} = require('../../constants/regex-patterns');
const {CA_COUNTRY_CODE} = require('../../constants/common');

class MembersImportService {
    constructor(AAUAPIService, ValidationService, QueueService, AauUtils) {
        this.AAUAPIService = AAUAPIService;
        this.ValidationService = ValidationService;
        this.QueueService = QueueService;
        this.Utils = AauUtils;
    }

    get queue () {
        return this.QueueService;
    }

    get process () {
        return new ImportProcessService(this.AAUAPIService, this.QueueService, this.Utils);
    }

    async create (masterClubID, clubOwnerID, params) {
        if(!masterClubID) {
            throw { validation: 'Master Club ID required' };
        }

        if(!clubOwnerID) {
            throw { validation: 'Club Owner ID required' };
        }

        if(
            !params ||
            !params.option ||
            ![this.Utils.IMPORT_MODE.DEFAULT, this.Utils.IMPORT_MODE.INSERT].includes(params.option)
        ) {
            throw { validation: 'Invalid option value' };
        }

        const clubHasAAUActiveImport = await this.QueueService.getClubImport(masterClubID);

        if(!_.isEmpty(clubHasAAUActiveImport)) {
            throw { validation: 'AAU Import is already running' };
        }

        const clubHasSEActiveImport = await SportEngineMemberService.import.queue.getClubImport(masterClubID);

        if(!_.isEmpty(clubHasSEActiveImport)) {
            throw { validation: 'Sport Engine Import is already running' };
        }

        let clubData = await this.getAAUClubData(masterClubID, clubOwnerID);

        if(_.isEmpty(clubData)) {
            throw { validation: 'Club not found' };
        }

        await this.__validateClubData(clubData, masterClubID);

        return this.QueueService.add(masterClubID, params.option);
    }

    getAAUClubData (masterClubID, clubOwnerID) {
        let query = knex('master_club AS mc')
            .select({
                country: 'mc.country',
                [this.Utils.AAU_FIELDS.CLUB_CODE]: 'mc.aau_club_code',
                [this.Utils.AAU_FIELDS.MEMBERSHIP_IDENTIFIER]: 'mc.aau_primary_membership_id',
                [this.Utils.AAU_FIELDS.ZIP_CODE]: 'mc.aau_primary_zip',
            })
            .where('mc.master_club_id', masterClubID)
            .where('mc.club_owner_id', clubOwnerID)

        return Db.query(query).then(result => result && result.rows[0] || null);
    }

    async __validateClubData (clubData, masterClubId) {
        let zipCode = clubData[this.Utils.AAU_FIELDS.ZIP_CODE].trim();

        let aauData;
        try {
            aauData = await this.ValidationService.processMember({
                aau_club_code: clubData[this.Utils.AAU_FIELDS.CLUB_CODE],
                aau_primary_membership_id: clubData[this.Utils.AAU_FIELDS.MEMBERSHIP_IDENTIFIER],
                aau_primary_zip: zipCode,
            });
        } catch (err) {
            if(clubData.country === CA_COUNTRY_CODE) {
                const isZipCodeContainsSpace = WHITE_SPACE_REGEX.test(zipCode);
                zipCode = isZipCodeContainsSpace ? this.__removeSpace(zipCode) : this.__addSpace(zipCode);

                aauData = await this.ValidationService.processMember({
                    aau_club_code: clubData[this.Utils.AAU_FIELDS.CLUB_CODE],
                    aau_primary_membership_id: clubData[this.Utils.AAU_FIELDS.MEMBERSHIP_IDENTIFIER],
                    aau_primary_zip: zipCode,
                });

                if(!_.isEmpty(aauData.memberData)) {
                    await this.__updateClubData(masterClubId, { aau_primary_zip: zipCode })
                }
            } else {
                throw err;
            }
        }

        if(!_.isEmpty(aauData.validationError)) {
            throw { validation: aauData.validationError.validation };
        }

        if(_.isEmpty(aauData.memberData)) {
            throw { validation:
                    `You're trying to import members from AAU but member with Membership Id 
                ${clubData[this.Utils.AAU_FIELDS.MEMBERSHIP_IDENTIFIER]} not found
                in AAU for club ${clubData[this.Utils.AAU_FIELDS.CLUB_CODE]}
                with primary zip ${clubData[this.Utils.AAU_FIELDS.ZIP_CODE]}.
                Please contact SW support for assistance.`
            }
        }
    }

    __removeSpace(zipCode) {
        return zipCode.replace(/\s/g, "");
    }

    __addSpace(zipCode) {
        return zipCode.replace(/^(.{3})(.*)$/, "$1 $2");
    }

    __updateClubData (masterClubId, aauData) {
        let query = knex('master_club AS mc')
            .update(aauData)
            .where('mc.master_club_id', masterClubId)

        return Db.query(query);
    }
}

module.exports = MembersImportService;
