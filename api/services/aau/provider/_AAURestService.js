'use strict';

const fetch = require('node-fetch');
const querystring = require('querystring');

const HDR_PUBLIC = 'X-Uventex-Public-Api-Key';
const HDR_SECRET = 'X-Uventex-Secret-Api-Key';

const AAUUtils = require('../../../lib/AAUUtilsService');
const FiltersValidationService = require('../_FiltersValidationService');

class _AAURestService {
    constructor() {
        this.cfg = () => sails?.config?.aauRest || {};
    }

    #DEFAULT_TIMEOUT = 10000;
    #DEFAULT_RETRY = { attempts: 1, backoffMs: 300 };

    get HDR_PUBLIC () {
        return HDR_PUBLIC;
    }

    get HDR_SECRET () {
        return HDR_SECRET;
    }
    
    get ENDPOINTS() {
        return {
            individualVerify: 'membership/individual/verify',
            clubVerify: 'membership/club/verify',
            clubRoster: 'membership/club/roster',
        }
    }

    get BASE_URL() {
        const { baseUrl } = this.cfg();
        if(!baseUrl) {
            throw new Error('AAU REST baseUrl is not configured');
        }
        return baseUrl;
    }

    get TIMEOUT() {
        const { timeoutMs } = this.cfg();
        return Number(timeoutMs) > 0 ? Number(timeoutMs) : this.#DEFAULT_TIMEOUT;
    }

    get RETRY() {
        const { retry = {} } = this.cfg();
        const attempts = Number(retry.attempts) >= 0
            ? Number(retry.attempts)
            : this.#DEFAULT_RETRY.attempts;

        const backoffMs = Number(retry.backoffMs) >= 0
            ? Number(retry.backoffMs)
            : this.#DEFAULT_RETRY.backoffMs;

        return { attempts, backoffMs };
    }

    get DEFAULT_HEADERS() {
        const { publicApiKey, secretApiKey } = this.cfg();
        const headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
        };
        if (publicApiKey) headers[this.HDR_PUBLIC] = publicApiKey;
        if (secretApiKey) headers[this.HDR_SECRET] = secretApiKey;
        return headers;
    }

    isRest() {
        return true;
    }

    async getMembersCanonical(filters) {
        this.#validateFilters(filters);
        const restQuery = this.#mapCanonicalFiltersToRest(filters);

        let dto;

        if(restQuery.primaryContactId) {
            dto = await this.getClubRoster(restQuery);
        } else {
            dto = await this.verifyIndividual(restQuery);
        }

        if(!dto) {
            return [];
        }

        const list = Array.isArray(dto)
            ? dto
            : (dto && (dto.members || dto.data || dto.results)) || (dto ? [dto] : []);
        return list.map(m => AAUUtils.mapRestDtoToCanonical(m));
    }

    async verifyIndividual(query = {}) {
        return this._get(this.ENDPOINTS.individualVerify, query);
    }

    async verifyClub(query = {}) {
        return this._get(this.ENDPOINTS.clubVerify, query);
    }

    async getClubRoster(query = {}) {
        return this._get(this.ENDPOINTS.clubRoster, query);
    }

    async _get(endpoint, query = {}) {
        const url = this._buildUrl(endpoint, query);
        return this._requestWithRetry(url, { method: 'GET', headers: this.DEFAULT_HEADERS, timeout: this.TIMEOUT });
    }

    _buildUrl(endpoint, query) {
        const base = this.BASE_URL.endsWith('/') ? this.BASE_URL : `${this.BASE_URL}/`;

        const url = new URL(endpoint, base);

        if (query && Object.keys(query).length) {
            url.search = querystring.stringify(query);
        }
        return url.toString();
    }

    async _requestWithRetry(url, options) {
        const { attempts, backoffMs } = this.RETRY;
        let lastErr;

        for(let i = 0; i <= attempts; i++) {
            try {
                const res = await fetch(url, options);
                if(!res.ok) {
                    await this._throwHttpError(res);
                }
                const text = await res.text();
                try { return text ? JSON.parse(text) : {}; } catch (_) { return text; }
            } catch (err) {
                lastErr = err;
                // Retry only on network errors and 5xx
                if(err && err._shouldNotRetry) break;
                if(i < attempts) {
                    await this._sleep(backoffMs * (i + 1));
                    continue;
                }
            }
        }
        throw lastErr || new Error('AAU REST request failed');
    }

    async _throwHttpError(res) {
        const status = res.status;
        let message = res.statusText || 'Request failed';

        try {
            const bodyText = await res.text();
            if (bodyText) {
                try {
                    const json = JSON.parse(bodyText);
                    message = json.message || json.error || message;
                } catch {}
            }
        } catch {}

        const error = new Error(message);
        error.status = status;

        if ([400, 401, 403, 404, 406, 409, 422].includes(status)) {
            error._shouldNotRetry = true;
            throw error;
        }

        if (status >= 500) {
            error._shouldNotRetry = false; // можна буде ретраїти
            throw error;
        }

        error._shouldNotRetry = true;
        throw error;
    }


    _sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Canonical wrappers
    async verifyIndividualCanonical(query = {}) {
        const dto = await this.verifyIndividual(query);
        if (Array.isArray(dto)) {
            const first = dto[0];
            return first ? AAUUtils.mapRestDtoToCanonical(first) : null;
        }
        return dto ? AAUUtils.mapRestDtoToCanonical(dto) : null;
    }

    #validateFilters (filters) {
        FiltersValidationService.validate(filters);
    }

    #mapCanonicalFiltersToRest(filters) {
        if(!filters[AAUUtils.AAU_FIELDS.CLUB_CODE]) {
            return {
                membershipId: filters[AAUUtils.AAU_FIELDS.MEMBERSHIP_IDENTIFIER],
                zipCode: filters[AAUUtils.AAU_FIELDS.ZIP_CODE] || undefined,
                lastName: filters[AAUUtils.AAU_FIELDS.LAST_NAME] || undefined,
                birthDate: filters[AAUUtils.AAU_FIELDS.BIRTH_DATE] || undefined,
                firstName: filters[AAUUtils.AAU_FIELDS.FIRST_NAME] || undefined,
            };
        } else {
            return {
                membershipId: filters[AAUUtils.AAU_FIELDS.CLUB_CODE],
                primaryContactId: filters[AAUUtils.AAU_FIELDS.MEMBERSHIP_IDENTIFIER]
            };
        }
    }
}

module.exports = new _AAURestService();
