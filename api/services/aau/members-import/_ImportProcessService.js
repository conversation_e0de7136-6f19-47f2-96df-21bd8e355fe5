const MemberImportService   = require('./_MemberImportService');
const { CANONICAL_MEMBERSHIP_TYPE } = require('../../../constants/aau/member');

class ImportProcessService {
    constructor(AAUService, QueueService, AAUUtils) {
        this.AAUService = AAUService;
        this.QueueService = QueueService;
        /**
         * @type {AAUUtilsService}
         */
        this.Utils = AAUUtils;

        this.season = sails.config.sw_season.current;
    }

    #MEMBER_CLUB_TABLE = {
        athlete: 'master_athlete',
        staff: 'master_staff',
    }

    get memberImport () {
        return new MemberImportService(this.Utils);
    }

    async run () {
        const queueData = await this.QueueService.smartPop();

        if (queueData === null) {
            return;
        }

        await this.QueueService.setRequested(queueData.id);

        const jsonOutput = await this.#importDataFromAPI(queueData)
            .catch(err => {
                this.QueueService.setResponded(queueData.id, err);
                throw err;
            });

        await RosterSnapshotService.clubSnapshot(
            queueData.master_club_id, this.season, queueData.import_option
        ).catch(err => {
            err.description = 'Roster Snapshot Error, ignored by parsing flow';

            loggers.errors_log.error(err);
        });

        return this.QueueService.setResponded(queueData.id, jsonOutput);
    }

    async #importDataFromAPI (queueData = {}) {
        this. #validateQueueData(queueData);

        const {
            import_option,
            master_club_id,
            aau_club_code,
            aau_primary_membership_id,
            aau_primary_zip
        } = queueData;

        const membersData = await this.#getClubMembersFromAauAPI(
            aau_club_code,
            aau_primary_membership_id,
            aau_primary_zip
        );

        if(!membersData.length) {
            return;
        }

        loggers.debug_log.verbose('Got', membersData.length, 'members from AAU API');

        const { adults, juniors } = await this.#formatMembersData(membersData);

        let tr;

        try {
            tr = await Db.begin();

            await this.#upsertMembers(tr, master_club_id, adults, this.Utils.MEMBER_TYPE.STAFF, import_option);
            await this.#upsertMembers(tr, master_club_id, juniors, this.Utils.MEMBER_TYPE.ATHLETE, import_option);

            await tr.commit();

            return membersData;
        } catch (err) {
            if(tr && !tr.isCommited) {
                await tr.rollback();
            }

            const logError = {
                error: (err && err.error)?err.error:err,
                master_club_id: master_club_id,
                aau_club_code: aau_club_code
            };

            loggers.errors_log.error(logError);

            ErrorSender.aauAPIError(logError);

            throw err;
        }
    }

    #validateQueueData ({ import_option, master_club_id, aau_club_code, aau_primary_membership_id, aau_primary_zip }) {
        if(!aau_club_code) {
            throw new Error('AAU Club Code required');
        }

        if(!aau_primary_membership_id) {
            throw new Error('Primary Membership ID required');
        }

        if(!aau_primary_zip) {
            throw new Error('Primary Zip required');
        }

        if(!import_option || ![this.Utils.IMPORT_MODE.DEFAULT, this.Utils.IMPORT_MODE.INSERT].includes(import_option)) {
            throw new Error('Invalid import mode option value');
        }

        if(!master_club_id) {
            throw new Error('Master Club ID required');
        }
    }

    async #getClubMembersFromAauAPI (aauClubCode, aauPrimaryMembershipId, aauPrimaryZip) {
        const params = {
            [this.Utils.AAU_FIELDS.CLUB_CODE]: aauClubCode,
            [this.Utils.AAU_FIELDS.MEMBERSHIP_IDENTIFIER]: aauPrimaryMembershipId,
            [this.Utils.AAU_FIELDS.ZIP_CODE]: aauPrimaryZip,
        };

        return this.AAUService.getMembersCanonical(params);
    }

    async #formatMembersData (members) {
        const results = {
            adults: [],
            juniors: []
        }

        for(let membership of Object.values(members)) {
            this.#prepareMember(membership, results);
        }

        return results;
    }

    #prepareMember(membership, results) {
        const memberType = membership.membershipType === CANONICAL_MEMBERSHIP_TYPE.ATHLETE
            ? this.Utils.MEMBER_TYPE.ATHLETE
            : this.Utils.MEMBER_TYPE.STAFF;

        this.memberImport.prepareMember(membership, memberType, results);
    }

    async #upsertMembers (tr, masterClubID, members, type, importOption) {
        for(const member of members) {
            await this.#upsertMember(
                tr,
                masterClubID,
                member,
                this.#MEMBER_CLUB_TABLE[type],
                importOption
            );
        }
    }

    async #upsertMember (tr, masterClubID, memberImportData, tableName, importOption) {
        const memberDBData = await this.#getMember(tr, masterClubID, memberImportData, tableName, importOption);

        const memberNotFoundInDB = _.isEmpty(memberDBData);

        if (memberNotFoundInDB) {
            await this.#insertMember(tr, masterClubID, memberImportData, tableName);
        } else {
            if(this.#isInsertImport(importOption)) {
                if(memberDBData.is_deleted) {
                    if(this.#memberHasAnotherSanctioning(memberDBData)) {
                        memberImportData = this.#setAnotherSanctioningsInvalid(memberImportData, memberDBData);
                    }

                    await this.#updateMember(tr, masterClubID, memberImportData, tableName, memberDBData?.id);
                } else {
                    if(!this.#memberHasActualSanctioning(memberDBData)) {
                        await this.#updateMember(tr, masterClubID, memberImportData, tableName, memberDBData?.id);
                    }
                }
            } else if(this.#isUpdateImport(importOption)) {
                if(memberDBData.is_deleted && this.#memberHasAnotherSanctioning(memberDBData)) {
                    memberImportData = this.#setAnotherSanctioningsInvalid(memberImportData, memberDBData);
                }

                await this.#updateMember(tr, masterClubID, memberImportData, tableName, memberDBData?.id);
            }
        }
    }

    async #getMember (tr, masterClubID, memberFromAPI, tableName) {
        const query = knex(tableName)
            .select({
                has_aau_sanctioning: knex.raw(`aau_membership_id IS NOT NULL`),
                has_usav_sanctioning: knex.raw(`usav_number IS NOT NULL`),
                id: `${tableName}_id`,
                is_deleted: knex.raw(`deleted IS NOT NULL`)
            })
            .where('master_club_id', masterClubID)
            .where((builder) => {
                builder
                    .where(builder => {
                        builder
                            .whereRaw('LOWER(TRIM(first)) = LOWER(TRIM(?))', `${memberFromAPI.first}`)
                            .whereRaw('LOWER(TRIM(last)) = LOWER(TRIM(?))', `${memberFromAPI.last}`)
                            .where('birthdate', memberFromAPI.birthdate)
                            .whereNull('aau_membership_id')
                    })
                    .orWhere('aau_membership_id', memberFromAPI.aau_membership_id)
            })
            .where('season', memberFromAPI.season)
            .orderByRaw('deleted IS NULL DESC',)
            .orderBy('created', 'desc')
            .limit(1);

        const { rows: [memberFromDb] } = await tr.query(query);

        return memberFromDb;
    }

    #insertMember (tr, masterClubID, member, tableName) {
        member = _.assign({ master_club_id: masterClubID, aau_sync: knex.fn.now() }, member);

        const tableIdName = `${tableName}_id`;
        const query = knex(tableName)
            .insert(member)
            .returning(tableIdName);

        return tr.query(query);
    }

    #isInsertImport (importOption) {
        return importOption === this.Utils.IMPORT_MODE.INSERT;
    }

    #memberHasActualSanctioning (memberFromDB) {
        return memberFromDB.has_aau_sanctioning;
    }

    #memberHasAnotherSanctioning (memberFromDB) {
        return memberFromDB.has_usav_sanctioning;
    }

    #setAnotherSanctioningsInvalid (member, memberFromDB) {
        if(memberFromDB.has_usav_sanctioning) {
            member.membership_status = 'incomplete';
        }

        return member;
    }

    #updateMember (tr, masterClubID, member, tableName, memberId) {
        member = _.assign({ aau_sync: knex.fn.now(), deleted: null }, member);

        const tableIdName = `${tableName}_id`;
        const query = knex(tableName)
            .update(member)
            .where(tableIdName, memberId);

        return tr.query(query);
    }

    #isUpdateImport (importOption) {
        return importOption === this.Utils.IMPORT_MODE.DEFAULT;
    }
}

module.exports = ImportProcessService;
