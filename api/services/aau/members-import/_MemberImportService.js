class MemberImportService {
    constructor(AAUUtils) {
        /**
         * @type {AAUUtilsService}
         */
        this.Utils = AAUUtils;

        this.season = sails.config.sw_season.current;
    }

    prepareMember(member, memberType, results) {
        const state = member.stateCode || '';
        const genderApp = this.Utils.mapCanonicalGenderToApp(member.gender);

        const formattedMemberData = {
            address: member.address,
            address2: member.address2,
            city: member.city,
            state: state.length > 2 ? state.slice(0, 2) : state,
            zip: member.zipCode,
            district: member.districtCode,
            birthdate: member.birthDate || null,
            email: member.email,
            first: this._prepareNameValue(member.firstName),
            last: this._prepareNameValue(member.lastName),
            gender: genderApp,
            aau_membership_id: member.membershipNumber,
            aau_membership_ending_year: member.endYear,
            season: this.season,
        };

        if (memberType === this.Utils.MEMBER_TYPE.ATHLETE) {
            const gradyear = Number(member.graduationYear);
            const formattedAthleteData = {
                ...formattedMemberData,
                age: this.Utils.getMinAge(member.birthDate),
                country: member.countryCode,
                ...(gradyear ? { gradyear } : {}),
                phonem: this._preparePhoneValue(member.phone),
            };

            results.juniors.push(formattedAthleteData);
        } else {
            const isEligible = this.season <= (member.endYear || 0);

            const formattedStaffData = {
                ...formattedMemberData,
                phoneh: this._preparePhoneValue(member.phone),
                aau_bg_screening: isEligible ? this.Utils.VALID_BACKGROUND_SCREENING_STATUS
                    : this.Utils.INVALID_BACKGROUND_SCREENING_STATUS,
                aau_bg_expire_date: member.endYear ? this.Utils.getSeasonEndByYear(member.endYear) : null
            };

            results.adults.push(formattedStaffData);
        }
    }

    prepareEmptyAauFields() {
        return {
            aau_membership_id: null,
            aau_membership_ending_year: null,
            district: null,
        };
    }

    _prepareNameValue(value) {
        if (_.isString(value)) {
            return value.trim();
        }

        return value;
    }

    _preparePhoneValue(value) {
        if(_.isNumber(value)) {
            return value;
        }

        return value && value.replace(/\D/g, '');
    }
}

module.exports = MemberImportService;
