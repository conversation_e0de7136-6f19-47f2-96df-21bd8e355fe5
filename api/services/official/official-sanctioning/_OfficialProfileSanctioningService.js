const moment = require("moment/moment");

class OfficialProfileSanctioningService {
    constructor(ProfileService, SportEngineUtils, AAUUtils) {
        this.SportEngineUtils = SportEngineUtils;
        this.AAUUtils = AAUUtils;
        this.ProfileService = ProfileService;
    }

    #SANCTIONING_TYPE = {
        USAV: 'usav',
        AAU: 'aau'
    }

    #SANCTIONING_NUMBER_FIELD = {
        [this.#SANCTIONING_TYPE.USAV]: 'usav_num',
        [this.#SANCTIONING_TYPE.AAU]: 'aau_number',
    }

    #SANCTIONING_FIELDS_FOR_SKIP = {
        [this.#SANCTIONING_TYPE.USAV]: ['usav_num', 'region', 'birthdate'],
        [this.#SANCTIONING_TYPE.AAU]: ['aau_number'],
    }

    async collectSanctioningsData (memberData, userData, officialId = null) {
        const memberWithoutUSAV = !memberData[this.#SANCTIONING_NUMBER_FIELD[this.#SANCTIONING_TYPE.USAV]];
        const memberWithoutAAU = !memberData[this.#SANCTIONING_NUMBER_FIELD[this.#SANCTIONING_TYPE.AAU]];

        const {
            memberData: processedUSAVMember,
            validationError: USAVValidationError
        } = await this.collectSanctioningData(memberData, userData, this.#SANCTIONING_TYPE.USAV, officialId);

        const {
            memberData: processedAAUMember,
            validationError: AAUValidationError
        } = await this.collectSanctioningData(memberData, userData, this.#SANCTIONING_TYPE.AAU, officialId);

        const failedUSAVSanctioning = memberWithoutUSAV || !_.isEmpty(USAVValidationError);
        const failedAAUSanctioning = memberWithoutAAU || !_.isEmpty(AAUValidationError);

        if(failedUSAVSanctioning && failedAAUSanctioning) {
            if(USAVValidationError) {
                throw USAVValidationError;
            }

            if(AAUValidationError) {
                throw AAUValidationError;
            }
        }

        let collectedMemberData = {
            ...memberData,
            ...processedUSAVMember,
            ...processedAAUMember
        };

        if(USAVValidationError) {
            collectedMemberData = this.__skipUpdateForSanctioningFields(collectedMemberData, this.#SANCTIONING_TYPE.USAV);
        }

        if(AAUValidationError) {
            collectedMemberData = this.__skipUpdateForSanctioningFields(collectedMemberData, this.#SANCTIONING_TYPE.AAU);
        }

        return {
            memberData: collectedMemberData,
            USAVValidationError,
            AAUValidationError
        }
    }

    async collectSanctioningData (memberData, userData, sanctioningType, officialId = null) {
        let sanctioningMemberData = {};

        const memberHasSanctioningNumber = !!memberData[this.#SANCTIONING_NUMBER_FIELD[sanctioningType]];

        if(memberHasSanctioningNumber) {
            sanctioningMemberData = await this.collectData(memberData, userData, sanctioningType, officialId);

            const sanctioningValidationFailed = !_.isEmpty(sanctioningMemberData.validationError);

            if(!sanctioningValidationFailed) {
                memberData = sanctioningMemberData.preparedData;
            }
        } else {
            memberData = this.applyNullsForSanctioningFields(memberData, sanctioningType);
        }

        return { memberData, validationError: sanctioningMemberData.validationError };
    }

    async collectData (memberData, userData, sanctioningType, officialId = null) {
        let data = {};

        if(sanctioningType === this.#SANCTIONING_TYPE.USAV) {
            data = await this.processUSAVMemberData(memberData, userData, officialId);
        } else if(sanctioningType === this.#SANCTIONING_TYPE.AAU) {
            data = await this.processAAUMemberData(memberData, userData, officialId);
        } else {
            throw new Error(`Sanctioning Type ${sanctioningType} is not supported`);
        }

        return data;
    }

    applyNullsForSanctioningFields (memberData, sanctioningType) {
        if(sanctioningType === this.#SANCTIONING_TYPE.USAV) {
            memberData = this.__setNullsInWebpointFields(memberData);
            memberData = this.__setNullsInUSAVFields(memberData);
        } else if(sanctioningType === this.#SANCTIONING_TYPE.AAU) {
            memberData = this.__setNullsInAAUFields(memberData);
        } else {
            throw new Error(`Type ${sanctioningType} is not supported`);
        }

        return memberData;
    }

    __skipUpdateForSanctioningFields(memberData, sanctioningType) {
        const fieldForSkip = this.#SANCTIONING_FIELDS_FOR_SKIP[sanctioningType];

        return _.omit(memberData, fieldForSkip);
    }

    async processUSAVMemberData(memberData, user, officialId = null) {
        user.birthdate = memberData.birthdate;
        user.region = memberData.region;

        let usavMemberData = {};

        try {
            usavMemberData = await this.__getUSAVMemberData(memberData.usav_num, user);
        } catch (err) {
            usavMemberData = { memberData: {}, validationError: err }
        }

        await this.__validateExpiredMembership(
            this.SportEngineUtils.PROFILE_COMPLETED_FIELD,
            usavMemberData.validationError,
            officialId
        );

        const emptyData = this.__setNullsInWebpointFields(memberData, officialId);
        const USAVMemberDataForUpdate = this.__getUSAVMemberDataForUpdate(usavMemberData.memberData);
        const preparedData = Object.assign(emptyData, USAVMemberDataForUpdate);

        return {
            preparedData,
            validationError: usavMemberData.validationError,
        }
    }

    async processAAUMemberData(memberData, user, officialId = null) {
        let aauMemberData = {};

        try {
            aauMemberData = await this.__getAAUMemberData(memberData.aau_number, user);
        } catch (err) {
            aauMemberData = { memberData: {}, validationError: err }
        }

        await this.__validateExpiredMembership(
            this.AAUUtils.PROFILE_COMPLETED_FIELD,
            aauMemberData.validationError,
            officialId
        );

        const emptyData = this.__setNullsInAAUFields(memberData, officialId);
        const AAUMemberDataForUpdate = this.__getAAUMemberDataForUpdate(aauMemberData.memberData);
        const preparedData = Object.assign(emptyData, AAUMemberDataForUpdate);

        return {
            preparedData,
            validationError: aauMemberData.validationError
        }
    }

    async __validateExpiredMembership(profileCompletedField, validationError = {}, officialId = null) {
        const {hasExpiredMembership} = validationError;

        if (officialId && hasExpiredMembership) {
            await this.__setProfileToNotCompleted(officialId, profileCompletedField);
        }
    }

    __getAAUMemberDataForUpdate(memberData) {
        if (_.isEmpty(memberData)) {
            return {};
        }

        const currentSeason = sails.config.sw_season.current;
        const endYear = memberData?.endYear;
        const isEligible = _.isNumber(endYear) ? endYear >= currentSeason : false;
        const seasonEndData = _.isNumber(endYear) ? this.AAUUtils.getSeasonEndByYear(endYear) : null;

        return {
            aau_data: JSON.stringify(memberData),
            aau_modified: moment().format(),
            aau_bg_screening: isEligible ? '2' : '1',
            aau_bg_expire_date: seasonEndData,
            aau_expire_date: seasonEndData,
            aau_safesport_statusid: isEligible ? '2' : '1',
            aau_safesport_end_date: seasonEndData,
        };
    }

    __setProfileToNotCompleted (officialId, profileCompletedField) {
        return this.ProfileService.updateProfileRow(null, { [profileCompletedField]: null}, officialId);
    }

    __setNullsInAAUFields(memberData) {
        const initialData = _.cloneDeep(memberData);

        return Object.assign(initialData, {
            aau_data: null,
            aau_modified: null,
            aau_bg_screening: null,
            aau_bg_expire_date: null,
            aau_expire_date: null,
            aau_safesport_statusid: null,
            aau_safesport_end_date: null,
        })
    }

    __getUSAVMemberDataForUpdate(memberData) {
        if (_.isEmpty(memberData)) {
            return {};
        }

        const isEligible =
            memberData[this.SportEngineUtils.SE_FIELDS.MEMBERSHIP_STATUS] === this.SportEngineUtils.ELIGIBLE_MEMBER_STATUS;

        return {
            sportengine_data: JSON.stringify(memberData),
            sportengine_modified: moment().format(),
            background_screening: isEligible ? '2' : '1',
            bg_expire_date: memberData[this.SportEngineUtils.SE_FIELDS.MEMBERSHIP_END_DATE],
            mbr_expire_date: memberData[this.SportEngineUtils.SE_FIELDS.MEMBERSHIP_END_DATE],
            safesport_start_date: memberData[this.SportEngineUtils.SE_FIELDS.MEMBERSHIP_START_DATE],
            safesport_end_date: memberData[this.SportEngineUtils.SE_FIELDS.MEMBERSHIP_END_DATE],
            safesport_statusid: isEligible ? '2' : '1',
        };
    }

    __setNullsInWebpointFields (memberData) {
        const initialData = _.cloneDeep(memberData);

        return Object.assign(initialData, {
            webpoint_data           : null,
            background_screening    : null,
            bg_expire_date          : null,
            mbr_expire_date         : null,
            webpoint_modified       : null,
            safesport_start_date    : null,
            safesport_end_date      : null,
            safesport_statusid      : null
        })
    }

    __setNullsInUSAVFields (memberData) {
        const initialData = _.cloneDeep(memberData);

        return Object.assign(initialData, {
            sportengine_data: null,
            sportengine_modified: null,
        })
    }

    async __getUSAVMemberData(usav, userData) {
        if(!usav) {
            return {};
        }

        const paramsSportEngine = {
            usav_code: usav,
            first: userData.first,
            last: userData.last,
            gender: userData.gender,
            birthdate: {
                date: userData.birthdate,
                format: 'YYYY-MM-DD'
            },
            region: userData.region
        };

        return SportEngineMemberService.validation.processMember(
            paramsSportEngine,
            SportEngineMemberService.validation.MEMBER_TYPE.official
        );
    }

    async __getAAUMemberData(aauNumber, userData) {
        if(!aauNumber) {
            return {};
        }

        const paramsAAU = {
            aau_number: aauNumber,
            last_name: userData.last,
        };

        return AauMemberService.validation.processOfficialMember(paramsAAU);
    }
}

module.exports = OfficialProfileSanctioningService;
