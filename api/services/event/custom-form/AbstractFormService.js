

class AbstractFormService {

    FORM_TYPE = null;
    SUBMITTER_TYPE = null;

    async submitForm (eventID, eventFormID, data, fields, submitterID, tr) {
        for(const {id: fieldID} of fields) {
            await this.#saveSubmittedFieldValue(eventFormID, fieldID, data[fieldID], submitterID, tr);
        }
    }

    async getDefaultValues () {
        return {};
    }

    async getFormResultsSQL () {
        throw new Error('Method should be implemented');
    }

    async checkIfFormSubmitted () {
        throw new Error('Method should be implemented');
    }

    async #saveSubmittedFieldValue (eventFormID, eventFormFieldID, value, submitterID, tr) {
        const query = knex('custom_form_submitted_field_value')
            .insert({
                custom_form_event_id: eventFormID,
                custom_form_field_id: eventFormFieldID,
                submitter_type: this.SUBMITTER_TYPE,
                submitter_id: submitterID,
                value: Array.isArray(value) ? JSON.stringify(value) : value
            });

        const { rowCount } = await tr.query(query);

        if(!rowCount) {
            throw new Error('Form not submitted');
        }
    }
}

module.exports = AbstractFormService;
