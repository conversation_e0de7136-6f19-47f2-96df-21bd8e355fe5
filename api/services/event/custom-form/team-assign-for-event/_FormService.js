
const {
    FORM_TYPE: { TEAM_ASSIGN_FOR_EVENT },
    SUBMITTER_TYPE: { ROSTER_CLUB }
} = require('../../../../constants/event-custom-form');
const AbstractFormService = require('../AbstractFormService');

class SoCalCupFormService extends AbstractFormService {
    constructor(props) {
        super(props);
    }

    FORM_TYPE = TEAM_ASSIGN_FOR_EVENT;
    SUBMITTER_TYPE = ROSTER_CLUB;

    async getDefaultValues (submitterID) {
        if(!submitterID) {
            return {};
        }

        const query = `
            SELECT club_name,
                   FORMAT('%s %s', mc.director_first, mc.director_last) director_name,
                   mc.administrative_email                              mailing_address,
                   mc.director_email                                    club_email,
                   mc.city                                              club_city,
                   mc.director_phone                                    club_phone
            FROM master_club mc
            WHERE mc.master_club_id = $1`;

        const { rows: [club] } = await Db.query(query, [submitterID]);

        if (_.isEmpty(club)) {
            throw { validation: 'Club not found' };
        }

        return club;
    }

    async checkIfFormSubmitted (eventFormID, submitterID)  {
        const query = knex('custom_form_event AS cfe')
            .select({
                formName: 'cfe.name',
                isNotSubmitted: knex.raw('COUNT(cfsfv.*) = 0')
            })
            .leftJoin('custom_form_submitted_field_value AS cfsfv', join => {
                join.on('cfe.custom_form_event_id', 'cfsfv.custom_form_event_id')
                    .andOn(knex.raw(`cfsfv.submitter_type = ?`, [this.SUBMITTER_TYPE]))
                    .andOn(knex.raw(`cfsfv.submitter_id = ?`, [submitterID]))
            })
            .leftJoin('master_club AS mc', 'mc.master_club_id', 'cfsfv.submitter_id')
            .where('cfe.custom_form_event_id', eventFormID)
            .where('cfe.type', this.FORM_TYPE)
            .groupBy('cfe.name');

        const { rows: [submittingData] } = await Db.query(query);

        return submittingData;
    }

    getFormResultsSQL (eventID, eventFormID, { submitterID } = {}) {
        const includeEmails = !!submitterID;
        const baseParams = [includeEmails, eventID, this.FORM_TYPE, eventFormID];
        const params = submitterID ? [...baseParams, submitterID] : baseParams;
        return knex.raw(`
           SELECT
               (json_agg(json_build_object(
                   'value', (
                       CASE
                            WHEN cfet.type = 'multiselect' THEN
                                CASE
                                    WHEN cfsfv.value IS NULL OR cfsfv.value = '' THEN '[]'::json
                                    WHEN left(btrim(cfsfv.value), 1) = '[' THEN cfsfv.value::json
                                    WHEN POSITION(',' IN cfsfv.value) > 0 THEN to_json(
                                        ARRAY(SELECT btrim(x) FROM unnest(string_to_array(cfsfv.value, ',')) AS x)
                                    )
                                    ELSE to_json(ARRAY[cfsfv.value])
                                END
                            WHEN cfet.type = 'signature' AND cfsfv.value IS NOT NULL THEN to_json('Signed'::text)
                            WHEN cfet.type = 'signature_checkbox' AND cfsfv.value::BOOLEAN IS TRUE THEN to_json('Signed'::text)
                            WHEN cfet.type = 'date' AND cfsfv.value IS NOT NULL THEN to_json(TO_CHAR(cfsfv.value::timestamp, 'MM/DD/YY'))
                            WHEN cfet.type = 'checkbox' AND cfsfv.value::BOOLEAN IS NOT TRUE THEN to_json(''::text)
                            ELSE to_json(cfsfv.value)
                        END
                    ),
                   'label', (CASE WHEN ?::BOOLEAN THEN cff.label ELSE FORMAT('%s (Sort ID: %s)', cff.label, cff.sort_order) END),
                   'type', cfet.type,
                   'options', (CASE WHEN cfet.type IN ('select', 'multiselect') THEN cff.options END)
               ) ORDER BY cff.section, cff.sort_order)) AS fields,
               FORMAT('%s %s', mc.director_first, mc.director_last) AS purchaser_name,
               e.long_name AS event_name,
               cfe.event_id AS event_id,
               concat_ws(
                        '${EmailService.MULTIPLE__NOT_FORMATTED_RECEIVERS_STRING}', 
                        mc.director_email, 
                        mc.administrative_email
               ) AS email,
               TO_CHAR(MIN(cfsfv.created), 'Mon DD, YYYY, HH12:MI AM') AS submitted_at,
               TO_CHAR(MIN(cfsfv.created::timestamptz AT TIME ZONE e.timezone), 'Mon DD, YYYY, HH12:MI AM') AS submitted_at_event_tz
           FROM custom_form_submitted_field_value cfsfv
               JOIN custom_form_field cff ON cfsfv.custom_form_field_id = cff.custom_form_field_id
               JOIN custom_form_event cfe ON cfe.custom_form_event_id = cfsfv.custom_form_event_id
               JOIN event e ON e.event_id = cfe.event_id
               JOIN custom_form_field_type cfet ON cfet.custom_form_field_type_id = cff.custom_form_field_type_id
               JOIN master_club mc
                   ON mc.master_club_id = cfsfv.submitter_id
                   AND cfsfv.submitter_type = 'roster_club'::custom_form_submitter_type
           WHERE cfe.event_id = ?
             AND cfe.type = ?
             AND cfe.custom_form_event_id = ?
             ${submitterID ? 'AND cfsfv.submitter_id = ?' : ''}
           GROUP BY cfe.custom_form_event_id, e.long_name, cfe.event_id, mc.master_club_id`, params).toString();
    }
}

module.exports = new SoCalCupFormService();
