
const {
    FORM_TYPE: { CAMPS_PURCHASE_PAGE },
    SUBMITTER_TYPE: { PURCHASE }
} = require('../../../../constants/event-custom-form');
const AbstractFormService = require('../AbstractFormService');

class SoCalCupFormService extends AbstractFormService {
    constructor(props) {
        super(props);
    }

    FORM_TYPE = CAMPS_PURCHASE_PAGE;
    SUBMITTER_TYPE = PURCHASE;

    getFormResultsSQL (eventID, eventFormID, { submitterID } = {}) {
        const params = [eventID, this.FORM_TYPE, eventFormID];
        if (submitterID) {
            params.push(submitterID);
        }
        return knex.raw(`
            SELECT
                (json_agg(json_build_object(
                    'value', (
                        CASE
                            WHEN cfet.type = 'multiselect' THEN
                                CASE
                                    WHEN cfsfv.value IS NULL OR cfsfv.value = '' THEN '[]'::json
                                    WHEN left(btrim(cfsfv.value), 1) = '[' THEN cfsfv.value::json
                                    WHEN POSITION(',' IN cfsfv.value) > 0 THEN to_json(
                                        ARRAY(SELECT btrim(x) FROM unnest(string_to_array(cfsfv.value, ',')) AS x)
                                    )
                                    ELSE to_json(ARRAY[cfsfv.value])
                                END
                            WHEN cfet.type = 'signature' AND cfsfv.value IS NOT NULL THEN to_json('Signed'::text)
                            WHEN cfet.type = 'signature_checkbox' AND cfsfv.value::BOOLEAN IS TRUE THEN to_json('Signed'::text)
                            WHEN cfet.type = 'date' AND cfsfv.value IS NOT NULL THEN to_json(TO_CHAR(cfsfv.value::timestamp, 'MM/DD/YY'))
                            WHEN cfet.type = 'checkbox' AND cfsfv.value::BOOLEAN IS NOT TRUE THEN to_json(''::text)
                            ELSE to_json(cfsfv.value)
                        END
                    ),
                    'label', cff.label,
                    'type', cfet.type,
                    'options', (CASE WHEN cfet.type IN ('select', 'multiselect') THEN cff.options END)
                ) ORDER BY cff.section, cff.sort_order)) AS fields,
                FORMAT('%s %s', p.first, p.last) AS purchaser_name,
                p.email AS email,
                cfe.event_id AS event_id,
                e.long_name AS event_name,
                TO_CHAR(MIN(cfsfv.created), 'Mon DD, YYYY, HH12:MI AM') AS submitted_at,
                TO_CHAR(MIN(cfsfv.created::timestamptz AT TIME ZONE e.timezone), 'Mon DD, YYYY, HH12:MI AM') AS submitted_at_event_tz
            FROM custom_form_submitted_field_value cfsfv
            JOIN custom_form_field cff ON cfsfv.custom_form_field_id = cff.custom_form_field_id
            JOIN custom_form_event cfe ON cfe.custom_form_event_id = cfsfv.custom_form_event_id
            JOIN event e ON e.event_id = cfe.event_id
            JOIN custom_form_field_type cfet ON cfet.custom_form_field_type_id = cff.custom_form_field_type_id
            JOIN purchase p ON p.purchase_id = cfsfv.submitter_id
                AND cfsfv.submitter_type = 'purchase'::custom_form_submitter_type
            WHERE cfe.event_id = ?
              AND cfe.type = ?
              AND cfe.custom_form_event_id = ?
              ${submitterID ? 'AND cfsfv.submitter_id = ?' : ''}
            GROUP BY cfe.custom_form_event_id, p.purchase_id, e.long_name, cfe.event_id;`, params).toString();
    }
}

module.exports = new SoCalCupFormService();
