const AAUService = require('./AAUService');
const ImportService = require('./aau/_ImportService');
const QueueService = require('./aau/members-import/_QueueService');
const UtilsService = require('../lib/AAUUtilsService');
const ValidationService = require('./aau/_ValidationService');
const AAURestService = require('./aau/provider/_AAURestService');

class AauMemberFacade {
    constructor({ aauSoap, aauRest, queue, utils }) {
        this.aauSoap = aauSoap;
        this.aauRest = aauRest;

        let aauApi = sails.config.aauRest.enabled ? this.aauRest : this.aauSoap;

        this.validation = new ValidationService(aauApi, utils);
        this.import = new ImportService(aauApi, this.validation, queue, utils);
    }
}

const aauMember = new AauMemberFacade({
    aauSoap: AAUService,
    aauRest: AAURestService,
    queue: QueueService,
    utils: UtilsService,
});

module.exports = aauMember;
