// AAU Member-related constants (canonical and app-facing)

const CANONICAL_GENDER = Object.freeze({
  MALE: 'MALE',
  FEMALE: 'FEMALE',
  OTHER: 'OTHER',
  UNKNOWN: 'UNKNOWN',
});

const APP_GENDER = Object.freeze({
  MALE: 'male',
  FEMALE: 'female',
  OTH<PERSON>: 'non-binary',
});

const CANONICAL_MEMBERSHIP_TYPE = Object.freeze({
  ATHLETE: 'ATHLETE',
  NON_ATHLETE: 'NON_ATHLETE',
  OTHER: 'OTHER',
});

const DATE_FORMATS = Object.freeze({
  ISO_DATE: 'YYYY-MM-DD',
});

// Source/Provider raw codes and tokens
const SOAP_GENDER = Object.freeze({ MALE: 'M', FEMALE: 'F', OTHER: 'NB' });
const REST_GENDER_ALIASES = Object.freeze({ NON_BINARY: 'NON_BINARY', NON_BINARY_DASH: 'NON-BINARY' });

const SOAP_MEMBERSHIP_TYPE = Object.freeze({ ATHLETE: 'A', NON_ATHLETE: 'N' });
const REST_MEMBERSHIP_TOKENS = Object.freeze({ ATHLETE: 'ATHLETE', NON: 'NON', ADULT: 'ADULT' });

module.exports = {
  CANONICAL_GENDER,
  APP_GENDER,
  CANONICAL_MEMBERSHIP_TYPE,
  DATE_FORMATS,
  SOAP_GENDER,
  REST_GENDER_ALIASES,
  SOAP_MEMBERSHIP_TYPE,
  REST_MEMBERSHIP_TOKENS,
};

