'use strict';

const
    spawn           = require('child_process').spawn,
    createSchema    = require('json-gate').createSchema,
    path            = require('path'),
    argv            = require('optimist').argv,
    fs              = require('fs'),
    moment          = require('moment'),
    swUtils         = require('../../lib/swUtils'),
    csvUtils        = require('../../lib/csvUtils'),
    teamsConstants  = require('../../constants/teams');

const
    LOGS_FOLDER_PATH    = path.resolve(__dirname, '..', '..', '..', '..', 'logs'),
    APP_LOG             = path.resolve(LOGS_FOLDER_PATH, `${argv.prod?'prod':'dev'}_application.log`),
    ERR_LOG             = path.resolve(LOGS_FOLDER_PATH, `${argv.prod?'prod':'dev'}_errors.log`);

const SAILS_CONNECTION = sails.config.connections[sails.config.db.connection];

const headerReportsText = `USA Volleyball
Tournament Finisher Form
"Version 1.0, ${moment().format('MMM, YYYY')}"\n\n
Form instructions:
> Do not change any of the column names or add/remove columns.
> Required fields are denoted with a *. All other fields are optional.
> Please enter the data in the exact format described below. Failure to do so will result in your submission not being processed.\n\n\n`;

var ICON_TEMPLATE = '<span class="{0}" title="{1}" tabid="{2}"></span>';

var UPDATE_SCHEMA = createSchema({
    type: 'object',
    properties: {
      team_name: {
        type: ['string', 'null'],
        required: false,
        minLength: 1,
        maxLength: 200
      },
      organization_code: {
        type: ['string', 'null'],
        required: false,
        minLength: 1,
        maxLength: 12
      },
      division_id: {
        type: ['integer', 'null'],
        required: false
      },
      gender: {
        type: ['string', 'null'],
        required: false,
        enum: ['male', 'female', 'coed']
      },
      age: {
        type: ['integer', 'null'],
        required: false
      },
      rank: {
        type: ['integer', 'string', 'null'],
        required: false
      },
      deleted: {
        type: ['date', 'null'],
        required: false
      },
      status_entry: {
        type: ['integer', 'null'],
        required: false
      },
      date_entered: {
        type: ['date', 'null'],
        required: false
      },
      distance: {
        type: ['integer', 'null'],
        required: false
      },
      is_local: {
        type: ['boolean', 'null'],
        required: false
      }
    },
    dependencies: {
        team_name: 'organization_code',
        organization_code: 'team_name'
    },
    additionalProperties: false
});

// V2
module.exports = {
    // get /api/v2/event/:event/teams
    all: function (req, res) {
        let $eventID   = req.params.event;

        let sqlParams = _.defaults(req.query, { page: 1, event: $eventID });

        let sqlFields = [
            { column: 'rt.roster_team_id' },
            { column: 'rt.team_name' },
            { column: 'rt.organization_code' },
            { column: 'd.short_name', alias: 'division_name'},
            { column: 'rc.club_name' },
            { column: 'rt.manual_club_name' },
            { column: 'rt.locked' },
            { column: 'e.has_rosters'},
            { column: 'e.has_status_housing'},
            { column: 'rt.status_entry::INT' },
            { column: 'rt.status_paid::INT' },
            { column: 'rt.status_housing::INT' },
            { column: 'rt.division_id::INT' },
            { column: 'rt.seasonality' },
            { column: 'to_char(rt.date_entered::TIMESTAMPTZ at time zone e.timezone, \'MM/DD/YYYY, HH12:MI AM\')', alias: 'entered_date'},
            { column: 'COALESCE(rt.is_local, rc.is_local)', alias: 'is_local' },
            {
                column: '(CASE WHEN COALESCE(rt.is_local, rc.is_local) IS TRUE THEN \'Local\' ' +
                        'ELSE TO_CHAR(rt.date_housing::TIMESTAMPTZ at time zone e.timezone, \'MM/DD/YYYY, HH12:MI AM\') END)',
                alias: 'housing_date'
            },
            { column: 'to_char(p.created::TIMESTAMPTZ at time zone e.timezone, \'MM/DD/YYYY, HH12:MI AM\')', alias: 'created_date'},
            { column: 'to_char(p.date_paid::TIMESTAMPTZ at time zone e.timezone, \'MM/DD/YYYY, HH12:MI AM\')', alias: 'paid_date'},
            { column: 'count(rt.*) OVER()::INT', alias: 'count'},
            { column: 'rt.roster_athletes_count' },
            { column: 'rt.roster_staff_count' },
            { column: 'rt.is_valid_roster' },
            {
                column: `mc.profile_completed_at IS NOT NULL`,
                alias: 'profile_completed'
            },
            { column: 'rc.distance_to_event' },
            { column: 'p.status' },
            {
                column: 'p.type',
                alias: 'purchase_type',
            },
        ];

        //"autoQuoteTableNames: false" here is to make LATERAL JOIN work
        let query = squel.select({ autoQuoteTableNames: false }).from('roster_team', 'rt')
            .left_join(
                `LATERAL (SELECT *
                    FROM purchase_team AS pt
                    WHERE pt.roster_team_id = rt.roster_team_id
                      AND (pt.canceled IS NULL)
                      AND (pt.event_id = rt.event_id)
                    ORDER BY pt.created DESC
                    LIMIT 1)`,
                'ptm', 'TRUE'
            )
            .left_join('purchase', 'p', `p.purchase_id = ptm.purchase_id
                AND (p.status = 'paid' or (p.status = 'pending' and p.type = 'ach'))`);
        query = _queryBuilder(query, sqlParams, sqlFields);

        Db.query(query).then(function (result) {
            let count = 0;

            let teams = result.rows.map(item => {
                if(count !== item.count) {
                    count = item.count;
                }

                item.icons = `${_statusEntryIcon(item.status_entry)}&nbsp;${_statusPaymentIcon(item.status_paid)}`;

                if(item.status_housing && item.has_status_housing) {
                    item.icons += ('&nbsp;' + _statusHousingIcon(item.status_housing));
                }

                if(item.has_rosters) {
                    item.icons += ('&nbsp;' + _statusRosterIcon(item.is_valid_roster));
                }

                item.count = undefined;

                return item;
            });

            res.status(200).json({ teams, count });
        }).catch(err => {
            res.customRespError(err);
        });
    },
    // get /api/v2/event/:event/teams/:team/info
    find: function (req, res) {
        var $event_id = +req.params.event;
        var $team_id = +req.params.team;

        if(!$event_id) return res.status(400).json({error: 'No event provided'});
        if(!$team_id) return res.status(400).json({error: 'No team provided'});

        var query =
            `SELECT  
                  rt.roster_team_id,  rt.team_name, rt.organization_code,  
                  rt.event_id, rt.master_team_id, rt.division_id, rt.age::INT,  
                  rt.gender, rt.deleted, rt.rank, rt.master_team_id, rt.locked,  
                  rt.date_entered, rc.is_local AS club_is_local, rt.date_entered, rt.status_checkin,
                  rt.date_completed, rt.status_entry, rt.status_housing, rt.is_local "team_is_local",
                  (COALESCE(rt.ths_loyalty, 0) = 1) "ths_loyalty", rt.manual_club_name,
                  rt.status_paid, rt.status_roster, rt.distance, rt.organization_code,  
                  rt.total_tentative, rt.total_accepted, rt.total_confirmed, mc.administrative_email,  
                  rt.max_total_accepted, rc.code, rc.region, r."name" "region_name", rc.zip, rc.city,  
                  rc.state, rc.address, rc.country, c."name" "country_name", rc.roster_club_id,  
                  rc.club_name, rc.total_tentative club_total_tentative,  
                  rc.total_accepted club_total_accepted, rc.total_confirmed club_total_confirmed,  
                  rc.max_total_accepted club_max_total_accepted, e.reg_fee, e.event_id,  
                  COALESCE(d.credit_surcharge, e.credit_surcharge, 0) AS surcharge, d.name AS "division_name", INITCAP(rt.seasonality) "seasonality",  
                  mc.director_first, mc.director_last, mc.director_email, rt.ths_trav_coord_phone, 
                  mc.director_phone, co.created AS director_created, rt.ths_trav_coord_name, rt.ths_trav_coord_email, 
                  co.user_id AS director_user_id, u.email AS director_user_email, 
                  TO_CHAR (rt.online_checkin_date::TIMESTAMPTZ AT TIME ZONE e.timezone, 'MM/DD/YYYY, HH12:MI AM') "online_checkin_date", 
                  rt.extra ->> 'show_accepted_bid' "show_accepted_bid", rt.extra ->> 'prev_qual' "prev_qual", 
                  rt.extra ->> 'prev_qual_age' "prev_qual_age", rt.extra ->> 'prev_qual_division' "prev_qual_division",
                  rt.roster_validated_by = 'eo' "validated_by_eo", rt.extra ->> 'earned_at' "earned_at", d.reg_fee "division_reg_fee",
                  CAST(rt.extra ->> 'bid_agreement_accepted' AS BOOLEAN) "bid_agreement_accepted", 
                  d.is_qualifying "is_qualifying_division",
                  ( 
                        SELECT ROW_TO_JSON(mt)  
                        FROM ( 
                              SELECT 
                                  mt.master_team_id, mt.team_name, mt.organization_code,  
                                  mt.rank, mt.age, mt.sport_id, mt.gender, mt.deleted, 
                                  mt.sport_variation_id 
                        ) mt 
                  ) master_team,
                  mc.profile_completed_at IS NOT NULL AS profile_completed,
                  (NOW() AT TIME ZONE e.timezone > e.date_end) AS event_is_ended,
                  e.online_team_checkin_mode,
                  e.online_team_checkin_available
             FROM roster_team rt  
             LEFT JOIN roster_club rc ON rt.roster_club_id = rc.roster_club_id  
             LEFT JOIN master_club mc ON mc.master_club_id = rc.master_club_id  
             LEFT JOIN club_owner co ON co.club_owner_id = mc.club_owner_id  
             LEFT JOIN master_team mt ON mt.master_team_id = rt.master_team_id 
             LEFT JOIN "user" u ON co.user_id = u.user_id  
             LEFT JOIN division d ON (rt.division_id = d.division_id) 
             LEFT JOIN "event" e ON rt.event_id = e.event_id  
             LEFT JOIN "country" c ON c."code" = rc."country"
             LEFT JOIN "region" r ON r."region" = rc."region"
             WHERE rt.roster_team_id = $1  
             AND rt.deleted IS NULL 
             AND rt.event_id = $2`;

        Db.query(query, [$team_id, $event_id]).then(function (result) {
            var team = (result && result.rows && result.rows[0]);
            if(!team) {
                res.status(200).json({ team: {} });
            } else {
                var _login_hash = CDHashService.generate(team.director_user_id);
                if(_login_hash) team.directors_token = _login_hash;
                var _eo_restore_hash = CDHashService.generate(req.user.user_id);
                if(_eo_restore_hash) {
                    team.eo_restore_hash = _eo_restore_hash;
                    team.eo_email = req.user.email;
                }

                team.director_user_id = undefined;
                team.director_created = undefined;

                res.status(200).json({ team });
            }
        }).catch(res.customRespError.bind(res))
    },
    //post /api/v2/event/:event/teams/export
    xlsx_export: function (req, res) {
        let $event_id = req.params.event;

        let query = _queryBuilder(
            squel.select().from('roster_team', 'rt'),
            _.defaults(_.omit(req.body, 'limit', 'page', 'revert'), { event: $event_id }), [
            { column: 'rt.team_name',           alias: '"Team name"' },
            { column: 'd.name',                 alias: '"Division name"' },
            { column: 'rt.organization_code',   alias: '"USAV code"' },
            { column: 'rt.gender',              alias: '"Gender"' },
            { column: 'rc.city',                alias: '"City"' },
            { column: 'rc.state',               alias: '"State"' },
            { column: 'rc.club_name',           alias: '"Club name"' },
            { column: 'mc.director_email',      alias: '"Director Email"' },
            { column: 'mc.director_phone',      alias: '"Director Phone"' },
            { column: 'mc.director_first',      alias: '"Director First"' },
            { column: 'mc.director_last',       alias: '"Director Last"' },
            { column: 'COALESCE(rt.wristbands_count_athletes, 0)::VARCHAR', alias: '"Player Count"' },
            { column: 'COALESCE(rt.wristbands_count_staff, 0)::VARCHAR',    alias: '"Staff Count"' },
            { column: 'COALESCE(rc.address, mc.address)', alias: '"Address"' },
            { column: 'COALESCE(rc.zip, mc.zip)', alias: '"Zip"' },
            { column: 'rc.distance_to_event',   alias: '"Distance to Event (Miles)"' },
        ]);

        XLSXService.export(query.toString(), 'teams').then( fileName => {
            res.status(200).json({file_name: fileName});
        }).catch(res.customRespError.bind(res));
    },
    // get /api/v2/event/:event/teams/download/:fname
    exportFileDownload: function (req, res) {
      let fName = req.params.fname;

      if (!fName) {
          return res.render('500', { error: 'File name required' });
      }

      let filePath = path.resolve(__dirname, '..', '..', '..', '..', 'export', `${fName}.xlsx`);

      res.download(filePath, err => {
          if (err) {
              loggers.errors_log.error(err);
              if(err.code === 'ENOENT') {
                res.render('500', { error: 'File not found' });
              } else {
                res.serverError();
              }
          } else {
              fs.unlinkSync(filePath);
          }
      });
    },
    // post /api/v2/event/:event/teams/entry/change
    change_entry: async function (req, res) {
        let $status     = parseInt(req.body.status, 10),
            $event_id   = req.params.event,
            $notes      = req.body.notes || null,
            $teams      = req.body.teams,
            eoID        = req.session.passport.user.event_owner_id;

        let queryBuilderParams = _.defaults(_.omit(req.body, 'limit', 'page', 'revert'), { event: $event_id });

        if(!$status) {
            return res.validation('No status provided');
        }

        if(!$event_id) {
            return res.validation('No event identifier provided');
        }

        let sql =
            squel.update().table('roster_team', 'rt1')
            .set('status_entry', $status)
            .set('date_accepted', ($status === 12)?'NOW()':null )
            .where('rt1.status_entry <> ?', $status)
            .returning('roster_team_id, event_id, roster_club_id');

        if(Array.isArray($teams) && $teams.length) {
            sql.where(`roster_team_id IN (${swUtils.numArrayToString($teams)})`);
        } else {

            let subSQL = _queryBuilder(squel.select().from('roster_team', 'rt'), queryBuilderParams);
            subSQL.field('rt.roster_team_id');

            // find teams
            let teamsResult = await Db.query(subSQL);
            const teams = teamsResult.rows;

            if(teams.length === 0) {
                return res.status(400).json({ validation: 'No Teams found' });
            } else {

                // re-make sub query, replace single quote to double quote in search string
                queryBuilderParams.duplicateSingleQuote = true;
                subSQL = _queryBuilder(squel.select().from('roster_team', 'rt'), queryBuilderParams);
                subSQL.field('rt.roster_team_id');

            }

            subSQL.where('rt1.roster_team_id = rt.roster_team_id');
            sql.where(`EXISTS (${subSQL})`);

        }

        Db.query(sql)
        .then(result => result.rows)
        .then(teams => {
            if(teams.length > 0) {
                let action = getNotifAction($status);

                let add = addNotif.bind(null, $event_id, eoID, action, $notes);

                return swUtils.splitArray(teams).reduce((prevStep, teamsSublist) => {
                    return prevStep.then(() => {
                        loggers.debug_log.verbose('Adding notification for', teamsSublist.length, 'teams');
                        return Promise.all(teamsSublist.map(add))
                    })
                }, Promise.resolve())
                .then(() => teams);
            } else {
                return teams;
            }
        }).then(teams => {
            teams = teams.map(team => team.roster_team_id);

            res.status(200).json({ teams });
        }).catch(err => {
            if(err.message === 'DIVISION_IS_FULL') {
                return DivisionService.checkDivisionEntrance(queryBuilderParams)
                    .then(message => {
                        res.status(400).json({ validation: message });
                    }).catch(err => res.customRespError(err));
            }

            res.customRespError(err);
        });

        function addNotif (eventID, eventOwnerID, action, notes, team) {
            return eventNotifications.add_notification(eventID, {
                roster_team_id  : team.roster_team_id,
                roster_club_id  : team.roster_club_id,
                event_owner_id  : eventOwnerID,
                action          : action,
                comments        : notes
            });
        }

        function getNotifAction (status) {
            switch (status) {
                case 11:
                    return 'team.entry.declined';
                case 12:
                    return 'team.entry.accepted';
                case 13:
                    return 'team.entry.pending';
                case 14:
                    return 'team.entry.waiting';
                default:
                    return null;
            }
        }
    },

    //get /api/v2/event/:event/teams/finishes-report
    finishersReport: function (req, res) {
        let $event = parseInt(req.params.event, 10);

        let _query = `SELECT rt.organization_code, rt.team_name,
                          d.max_age "age", ds.rank, d.name "division_name"
                        FROM roster_team rt
                        INNER JOIN division d
                          ON d.division_id = rt.division_id
                        INNER JOIN division_standing ds
                          ON ds.division_id = rt.division_id
                             AND ds.event_id = rt.event_id
                             AND ds.team_id = rt.roster_team_id
                        WHERE rt.event_id = $1
                              AND rt.deleted IS NULL
                              AND rt.status_entry = 12
                              AND (ds.points_won > 0 OR ds.points_lost > 0)
                        ORDER BY d.sort_order, d.gender, d.max_age DESC, d.level_sort_order, d.level, ds.rank`;

        Promise.all([
            Db.query(
                `SELECT 
                        e.name "event_name", 
                        TO_CHAR(e.date_start, 'MM/DD/YYYY') "date_start",
                        FORMAT(
                            '%s-%s',
                            TO_CHAR(e.date_start, 'MM-DD-YYYY'),
                            e.name
                        ) "filename"
                     FROM "event" e
                     WHERE e.event_id = $1`,
                [$event]
            ).then(result => { return _.first(result.rows) }),
            Db.query(_query, [$event]).then(result => result.rows)
        ]).then( result => {
            let title = result[0];
            let teams = result[1];
            let filename = title.filename.replace(/([`~!@#$%^&*()\\[\]{};':"\\|,.<>\/? ”]+)/gi, '_');
            filename += "-finishers.csv";
            res.setHeader('Content-Type', 'text/csv');
            res.setHeader(
                'Content-Disposition',
                `attachment; filename=${filename}`);

            res.write(headerReportsText);

            res.write('Field explanation:\n');
            res.write('Finish,"Enter valid value for Finish i.e. 1,2,3"\n');
            res.write('Teamcode,Enter the valid FJCode for this team\n');
            res.write('Division, "Enter valid division i.e. Open, Club - National, Club - American"\n');
            res.write('Age,"Enter age of the competition, it should be valid number"\n');
            res.write('Teamname,Enter the human readable Team name\n\n\n');

            res.write('*Finish,*Teamcode,*Division,*Age,*Teamname,,,,,,\n\n');
            res.write('DO NOT MODIFY ANYTHING ABOVE THIS LINE. START ENTERING INFORMATION BELOW.\n\n');
            teams.forEach(t => {

                let division    = csvUtils.formatTextForCsv(t.division_name);
                let team        = csvUtils.formatTextForCsv(t.team_name);

                if(team && division && t.rank && t.organization_code && t.age && Number.isInteger(t.age)) {
                    res.write(`${t.rank},${t.organization_code},${division},${t.age},${team},,,,,,\n`);
                }
            });

            res.end();
        }).catch(err => {
            res.validation(err);
        });
    },

    //get /api/v2/event/:event/teams/head-to-head-report
    headToHeadReport: function (req, res) {
        let $event = parseInt(req.params.event, 10);

        let _query = `SELECT r.data::JSON, d.name "division_name", d.max_age "age",
                          ( SELECT rtm1.organization_code
                                FROM roster_team rtm1
                                WHERE rtm1.roster_team_id = m.team1_roster_id
                          ) "team_name1",
                          ( SELECT rtm2.organization_code
                                FROM roster_team rtm2
                                WHERE rtm2.roster_team_id = m.team2_roster_id
                          ) "team_name2"
                        FROM matches m
                          LEFT JOIN results r
                            ON r.event_id = m.event_id AND r.match_barcode = m.match_barcode
                          LEFT JOIN roster_team rt
                            ON rt.event_id = m.event_id AND m.division_id = rt.division_id
                          LEFT JOIN division d
                            ON d.division_id = m.division_id
                        WHERE m.event_id = $1
                        GROUP BY m.team1_roster_id, m.team2_roster_id, r.data, d.name, d.max_age, 
                                  d.sort_order, d.gender,d.level_sort_order,d.level
                        ORDER BY d.sort_order, d.gender, d.max_age DESC, d.level_sort_order, d.level`;

        Promise.all([
            Db.query(
                `SELECT 
                    e.name "event_name", 
                    TO_CHAR(e.date_start, 'MM/DD/YYYY') "date_start",
                    FORMAT(
                        '%s-%s',
                        TO_CHAR(e.date_start, 'MM-DD-YYYY'),
                        e.name
                    ) "filename"
                 FROM "event" e
                 WHERE e.event_id = $1`,
                [$event]
            ).then(result => { return _.first(result.rows) }),
            Db.query(_query, [$event]).then( result => result.rows )
            ]).then( result => {
            let title = result[0];
            let teams = result[1];
            let filename = title.filename.replace(/([`~!@#$%^&*()\\[\]{};':"\\|,.<>\/? ”]+)/gi, '_');
            filename += "-head-to-head.csv";

            res.setHeader('Content-Type', 'text/csv');
            res.setHeader(
                'Content-Disposition',
                `attachment; filename=${filename}`);

            res.write(headerReportsText);

            res.write('Field explanation:\n');
            res.write('Team,Enter Team name\n');
            res.write('Opponent,Enter Opponent Team name\n');
            res.write('Division,"Enter valid division i.e. Open, Club - National, Club - American"\n');
            res.write('Age,"Enter age of the competition, it should be valid number"\n');
            res.write('Win-Loss,Enter valid value for it i.e. W or L\n');
            res.write('Scores,numerical scores in column order with a comma seperating games\n\n\n');

            res.write('*Team,*Opponent,*Division,*Win-Loss,*Age,Scores,,,,,,\n\n');
            res.write('DO NOT MODIFY ANYTHING ABOVE THIS LINE. START ENTERING INFORMATION BELOW.\n\n');

            teams.forEach(team => {
                let matchScoresData = team.data || {};

                if (!_.isEmpty(matchScoresData)) {
                    let winner = Number(matchScoresData.winner) === 1 ? 'W' : 'L';
                    let sets   = '';

                    for (let i = 1; i < 6; i++) {
                        if (matchScoresData['set' + i]) {
                            sets += (i === 1 ? '' : ',') + matchScoresData['set' + i];
                        }
                    }

                    let teamOne     = csvUtils.formatTextForCsv(team.team_name1);
                    let teamTwo     = csvUtils.formatTextForCsv(team.team_name2);
                    let division    = csvUtils.formatTextForCsv(team.division_name);
                    sets            = csvUtils.formatTextForCsv(sets);

                    if(teamOne && teamTwo && division && winner && team.age && Number.isInteger(team.age)) {
                        res.write(`${teamOne},${teamTwo},${division},${winner},${parseInt(team.age)},${sets},,,,,,\n`);
                    }
                }
            });

            res.end();

        }).catch(err => {
            res.customRespError(err);
        })
    },

    //get /api/v2/event/:event/teams/usav-national-report
    usavNationalReport: async function (req, res) {
        const $eventId = parseInt(req.params.event, 10);

        try {
            const event = await Db.query(`
                SELECT e.event_id "id", e.name "name", e.sport_sanctioning_id "sport_sanctioning_id"
                FROM "event" e
                WHERE e.event_id = $1
                LIMIT 1
            `, [$eventId]).then(result => { return _.first(result.rows) });

            if (!event) {
                return res.status(404).json({ error: 'Event not found' });
            }

            if (event.sport_sanctioning_id !== teamsConstants.USAV_SANC_BODY.USAV) {
                return res.status(400).json({ error: 'Non USAV event is not allowed' });
            }

            const exportData = await Db.query(`
                SELECT e.name "event_name",
                    substr(m.display_name, 0, position('M' IN m.display_name)) "round",
                    CONCAT('Match ', m.match_number) "match",
                    ds1.rank "team_1_finish",
                    ds2.rank "team_2_finish",
                    rt1.team_name "team_1_name",
                    rt1.organization_code "team_1_code",
                    rt2.team_name "team_2_name",
                    rt2.organization_code "team_2_code",
                    CASE WHEN (m.results::JSON->>'winner')::INT = 1 THEN 'Won'
                    WHEN (m.results::JSON->>'winner')::INT = 2 THEN 'Lost'
                    ELSE 'Unknown result' END "result",
                    c.short_name "venue",
                    to_char(m.secs_start, 'MM/DD/YYYY') "date",
                    to_char(m.secs_start, 'HH12:MI AM') "time",
                    d.name "div",
                    split_part(
                        substr(m.results::JSON->'team1'->>'scores', 7, length(m.results::JSON->'team1'->>'scores')-7),
                        ',',
                        1
                    ) "score_1",
                    split_part(
                        substr(m.results::JSON->'team1'->>'scores', 7, length(m.results::JSON->'team1'->>'scores')-7),
                        ',',
                        2
                    ) "score_2",
                    split_part(
                        substr(m.results::JSON->'team1'->>'scores', 7, length(m.results::JSON->'team1'->>'scores')-7),
                        ',',
                        3
                    ) "score_3",
                    split_part(
                        substr(m.results::JSON->'team1'->>'scores', 7, length(m.results::JSON->'team1'->>'scores')-7),
                        ',',
                        4
                    ) "score_4",
                    split_part(
                        substr(m.results::JSON->'team1'->>'scores', 7, length(m.results::JSON->'team1'->>'scores')-7),
                        ',',
                        5
                    ) "score_5"
                FROM event e
                    INNER JOIN matches m ON e.event_id = m.event_id
                    INNER JOIN roster_team rt1 ON rt1.roster_team_id = m.team1_roster_id AND rt1.deleted IS NULL
                    INNER JOIN roster_team rt2 ON rt2.roster_team_id = m.team2_roster_id AND rt2.deleted IS NULL
                    INNER JOIN division d ON d.division_id = m.division_id
                    INNER JOIN division_standing ds1 ON ds1.team_id = rt1.roster_team_id AND ds1.event_id = e.event_id
                    INNER JOIN division_standing ds2 ON ds2.team_id = rt2.roster_team_id AND ds2.event_id = e.event_id
                    INNER JOIN courts c ON m.court_id = c.uuid
                WHERE e.event_id = $1
                ORDER BY DATE(m.secs_start), d.short_name, m.secs_start
            `, [$eventId]).then(result => result.rows);

            const filename = `${event.name}-usav-national-report.csv`;
            res.setHeader('Content-Type', 'text/csv');
            res.setHeader(
                'Content-Disposition',
                `attachment; filename=${filename}`);

            res.write('Event Name,Division Name,Team Name,Team Code,Opposing Team Name,Opposing Team Code,Match Date,Match Time,Outcome,Score 1,Score 2,Score 3,Score 4,Score 5\n');

            exportData.forEach(row => {
                const eventName = csvUtils.formatTextForCsv(row.event_name);
                const team1Name = csvUtils.formatTextForCsv(row.team_1_name);
                const team2Name = csvUtils.formatTextForCsv(row.team_2_name);
                const divName = csvUtils.formatTextForCsv(row.div);
                const score1 = row.score_1 || '';
                const score2 = row.score_2 || '';
                const score3 = row.score_3 || '';
                const score4 = row.score_4 || '';
                const score5 = row.score_5 || '';

                res.write(`${eventName},${divName},${team1Name},${row.team_1_code},${team2Name},${row.team_2_code},${row.date},${row.time},${row.result},${score1},${score2},${score3},${score4},${score5}\n`);
            });

            res.end();
        } catch (err) {
            res.customRespError(err);
        }
    },

    //get /api/v2/event/:event/teams/region-finishes-report
    regionFinishesReport: async function (req, res) {
        const allowedRegions = ['GE', 'SC'];
        const $eventId = parseInt(req.params.event, 10);

        try {
            const event = await Db.query(`
                SELECT e.event_id "id", e.name "name", e.region "region"
                FROM "event" e
                WHERE e.event_id = $1
                LIMIT 1
            `, [$eventId]).then(result => { return _.first(result.rows) });

            if (!event) {
                return res.status(404).json({ error: 'Event not found' });
            }

            if (!allowedRegions.includes(event.region)) {
                return res.status(400).json({ error: 'Region not allowed' });
            }

            return resultsExport($eventId, 'REGION-FINISHES-REPORT', event.region).then(filePath => {
                res.download(filePath);
            }).catch(err => {
                res.customRespError(err);
            });
        } catch (err) {
            res.customRespError(err);
        }
    },

    // get /api/v2/event/:event/teams/srva-report?region
    srvaReport: function (req, res) {
        let $eventId    = req.params.event,
            $region     = req.query.region;

        let sqlParams = [$eventId];

        if($region === 'SC') {
            return resultsExport($eventId, 'GEVA', $region).then(filePath => {
                res.download(filePath);
            }).catch(err => {
                res.customRespError(err);
            });
        }

        if($region) {
            sqlParams.push(`%${$region}`);
        }

        Promise.all([
            Db.query(
                `SELECT 
                    e.long_name "event_name", 
                    TO_CHAR(e.date_start, 'MM/DD/YYYY') "date_start",
                    FORMAT(
                        '%s %s',
                        TO_CHAR(e.date_start, 'MM-DD-YYYY'),
                        e.name
                    ) "filename"
                 FROM "event" e
                 WHERE e.event_id = $1`,
                [$eventId]
            ).then(result => { return _.first(result.rows) }),
            Db.query(
                `SELECT d.name, COALESCE(COUNT(rt.roster_team_id)::TEXT, '') "count"
                 FROM division d
                 INNER JOIN roster_team rt 
                     ON rt.division_id = d.division_id 
                     AND deleted IS NULL 
                     AND rt.status_entry = 12
                     ${(sqlParams.length === 2)?'AND rt.organization_code LIKE $2':''}
                 WHERE d.event_id = $1
                 GROUP BY d.name
                 ORDER BY d.name`,
                sqlParams
            ).then(result => { return result.rows || [] }),
            Db.query(
                `SELECT 
                    COALESCE(ds.rank::TEXT, '') "rank", d.name, rt.organization_code, rt.team_name
                 FROM roster_team rt
                 LEFT JOIN division d 
                     ON rt.division_id = d.division_id
                 LEFT JOIN division_standing ds 
                     ON rt.division_id = ds.division_id 
                     AND ds.team_id = rt.roster_team_id
                 WHERE rt.event_id = $1
                     AND rt.status_entry = 12
                     AND rt.deleted is NULL
                     ${(sqlParams.length === 2)?'AND rt.organization_code LIKE $2':''}
                 ORDER BY ds.rank, rt.team_name`,
                sqlParams
            ).then(result => { return result.rows || [] }),
            Db.query(
                `SELECT 
                    rt1.organization_code "tm1_code", 
                    rt2.organization_code "tm2_code",
                    COALESCE(SUBSTR(m.results::JSON->>'set1', 1, STRPOS(m.results::JSON->>'set1', '-')-1), '') tm1_set1,
                    COALESCE(SUBSTR(m.results::JSON->>'set1', STRPOS(m.results::JSON->>'set1', '-')+1), '') tm2_set1,
                    COALESCE(SUBSTR(m.results::JSON->>'set2', 1, STRPOS(m.results::JSON->>'set2', '-')-1), '') tm1_set2,
                    COALESCE(SUBSTR(m.results::JSON->>'set2', STRPOS(m.results::JSON->>'set2', '-')+1), '') tm2_set2,
                    COALESCE(SUBSTR(m.results::JSON->>'set3', 1, STRPOS(m.results::JSON->>'set3', '-')-1), '') tm1_set3,
                    COALESCE(SUBSTR(m.results::JSON->>'set3', STRPOS(m.results::JSON->>'set3', '-')+1), '') tm2_set3,
                    '' tm1_set4, '' tm2_set4, '' tm1_set5, '' tm2_set5, 'N' playoff, m.results
                 FROM matches m
                 INNER JOIN roster_team rt1 
                     ON m.team1_roster_id = rt1.roster_team_id
                 INNER JOIN roster_team rt2 
                     ON m.team2_roster_id = rt2.roster_team_id
                 WHERE m.event_id = $1
                     AND rt1.status_entry = 12
                     AND rt2.status_entry = 12
                     ${(sqlParams.length === 2)?'AND rt1.organization_code LIKE $2':''}
                     ${(sqlParams.length === 2)?'AND rt2.organization_code LIKE $2':''}`,
                sqlParams
            ).then(result => { return result.rows || [] })
        ]).then(sqlResult => {
            let title       = sqlResult[0],
                divisions   = sqlResult[1],
                placement   = sqlResult[2],
                results     = sqlResult[3];

            let filename = title.filename.replace(/([`~!@#$%^&*()\\[\]{};':"\\|,.<>\/? ”]+)/gi, '_');
            filename += " SO1.csv";

            res.setHeader('Content-Type', 'text/csv');
            res.setHeader(
                'Content-Disposition',
                `attachment; filename=${filename}`);

            res.write('[TITLE],,,,,,,,,,,,\n');
            res.write('TRN-DATE,TRN-NAME,,,,,,,,,,,\n');
            res.write(`${title.date_start},${title.event_name},,,,,,,,,,,\n`);
            res.write('[/TITLE],,,,,,,,,,,,\n');

            res.write('[DIVISIONS],,,,,,,,,,,,\n');
            res.write('DIVISION,NUM-TMS,,,,,,,,,,,\n');
            divisions.forEach(d => {
                res.write(`${d.name},${d.count},,,,,,,,,,,\n`);
            });
            res.write('[/DIVISIONS],,,,,,,,,,,,\n');


            res.write('[PLACEMENT],,,,,,,,,,,,\n');
            res.write('PLACEMENT,DIVISION,TM-CODE,TM-NAME,,,,,,,,,\n');
            placement.forEach(p => {
                res.write(`${p.rank},${p.name},${p.organization_code},${p.team_name},,,,,,,,,\n`);
            });

            res.write('[/PLACEMENT],,,,,,,,,,,,\n');

            res.write('[RESULTS],,,,,,,,,,,,\n');
            res.write(
                'TM1-CODE,TM2-CODE,TM1-SET1,TM2-SET1,TM1-SET2,' +
                'TM2-SET2,TM1-SET3,TM2-SET3,TM1-SET4,TM2-SET4,' +
                'TM1-SET5,TM2-SET5,PLAYOFF\n'
            );
            results.forEach(r => {
                res.write(
                    `${r.tm1_code},${r.tm2_code},${r.tm1_set1},${r.tm2_set1},${r.tm1_set2},` +
                    `${r.tm2_set2},${r.tm1_set3},${r.tm2_set3},${r.tm1_set4},${r.tm2_set4},` +
                    `${r.tm1_set5},${r.tm1_set5},${r.playoff}\n`
                );
            });
            res.write('[/RESULTS],,,,,,,,,,,,\n');

            res.end();

        }).catch(err => {
            res.customRespError(err);
        });
    },
    // get /api/v2/event/:event/teams/geva-report?region
    gevaExport: function (req, res) {
        let $eventId    = req.params.event,
            $region     = req.query.region;

        resultsExport($eventId, 'GEVA', $region).then(filePath => {
            res.download(filePath);
        }).catch(err => {
            res.customRespError(err);
        });
    },
    // get /api/v2/event/:event/teams/usav-final-report
    usavFinalReport: function (req, res) {
        let $eventId = req.params.event;

        resultsExport($eventId, 'USAV').then(filePath => {
            res.download(filePath)
        }).catch(err => {
            res.customRespError(err);
        })
    },
    // get /api/v2/event/:event/teams/export/:member_type
    exportMembers: function (req, res) {
        let $eventId    = req.params.event,
            $memberType = req.params.member_type;

        if(['staff', 'athlete'].indexOf($memberType) < 0) {
            return res.validation('Invalid Member Type');
        }

        let sql = Buffer.from(
            _queryBuilder(
                squel.select().from('roster_team', 'rt'),
                _.defaults(_.omit(req.query, 'limit', 'page', 'revert'), { event: $eventId }),
                [{ column: 'rt.roster_team_id' }]
            ).toString()
        ).toString('base64');

        let filePath = path.resolve(
            __dirname, '..', '..', '..', '..', 'export', `Members export ${new Date().getTime()}.xlsx`
        );

        new Promise((resolve, reject) => {
            let procKiller;

            let connectionStrBase64
                    = Buffer.from(JSON.stringify(SAILS_CONNECTION)).toString('base64');

            let log         = fs.openSync(APP_LOG, 'a'),
                errorsLog   = fs.openSync(ERR_LOG, 'a');

            let xslxProc = spawn('node', [
                'members_export.js',
                `--connection=${connectionStrBase64}`,
                `--path=${filePath}`,
                `--teams_query=${sql}`,
                `--type=${$memberType}`
            ], {
                detached    : true,
                cwd         : path.resolve(__dirname, '..', '..', '..', 'sw-utils'),
                stdio       : ['ignore', log, errorsLog]
            });

            xslxProc.on('error', err => {
                if(procKiller) { clearTimeout(procKiller); }
                reject(err);
            });

            xslxProc.on('close', code => {
                if(procKiller) { clearTimeout(procKiller); }
                if(code !== 0) {
                    reject();
                } else {
                    resolve();
                }
            });

            xslxProc.unref();

            procKiller = setTimeout(() => {
                xslxProc.kill();
            }, 10 * 1000);
        }).then(() => {
            res.download(filePath)
        }).catch(err => {
            if(!err) {
                res.view('500', { error: 'Empty result set. Nothing to export' });
            } else {
                res.customRespError(err);
            }
        })
    },
    // get /api/event/:event/roster/checkin
    printRosters: function (req, res) {
        let teamsPromise;

        if(req.query.team && req.query.team.length) {
            teamsPromise = Promise.resolve(req.query.team);
        } else {
            let sql = _queryBuilder(
                squel.select().from('roster_team', 'rt')
                .field('rt.roster_team_id', 'id'),
                _.defaults(_.omit(req.query, 'limit', 'page', 'revert'), { event: req.params.event })
            );

            teamsPromise = Db.query(sql).then(result => {
              return result.rows.map(team => team.id);
            })
        }

        teamsPromise.then(teams => {
            loggers.debug_log.verbose('Got', teams.length, 'teams to print');
            return CheckInRosterService.renderPDF(req.params.event, null, teams);
        }).then(data => {
            res.render('check_in_roster.ejs', data)
        }).catch(res.customRespError.bind(res))
    },
    // post /api/v2/event/:event/teams/change-division
    changeDivision: function (req, res) {
        let $eventID    = req.params.event,
            $divisionID = parseInt(req.body.dest_div, 10),
            $notes      = req.body.notes || null;

        let eoID        = req.session.passport.user.event_owner_id;

        let queryBuilderParams = _.defaults(_.omit(req.body, 'limit', 'page', 'revert'), { event: $eventID });

        let sql = _queryBuilder(squel.select().from('roster_team', 'rt'), queryBuilderParams);

        sql.field('rt.roster_team_id', 'id').field('rt.team_name', 'name').field('rt.roster_club_id');

        let tr;

        (async () => {
            // check event dates
            let result = await (Db.query(
                `SELECT e.event_id 
                 FROM "event" e
                 WHERE e.event_id = $1
                    AND e.date_end >= (NOW() AT TIME ZONE e.timezone)`,
                [$eventID]
            ));

            if(result.rows.length === 0) {
                return Promise.reject({ validation: 'Event is Closed.' });
            }

            // find division
            result  = await Db.query(
                `SELECT d.max_age::INTEGER "age", d.division_id "id"
                 FROM "division" d 
                 WHERE d.event_id = $1 
                    AND d.division_id = $2`,
                [$eventID, $divisionID]
            );

            const division = result.rows[0];

            if(_.isEmpty(division)) {
                return Promise.reject({ validation: 'Division Not Found' });
            }

            // find teams
            result = await Db.query(sql);

            const teams = result.rows;

            if(teams.length === 0) {
                return Promise.reject({ validation: 'No Teams found' });
            }

            tr = await Db.begin();

            // Change Division ID

            const change = changeTeamDivision.bind(null, tr, $eventID, division);

            const splittedTeamsList = swUtils.splitArray(teams);

            await (teams.reduce((prevStep, team) => {
                return prevStep.then(() => {

                    loggers.debug_log.verbose('Changing division for', teams.length, 'teams');

                    return change(team);
                });
            }, Promise.resolve()));

            // Add Notifications
            await splittedTeamsList.reduce(
                (prevStep, teamsSublist) => {
                    return prevStep.then(() => {
                        loggers.debug_log.verbose('Adding notifications for', teamsSublist.length, 'teams');

                        return Promise.all(
                            teamsSublist.map(team => {
                                return eventNotifications.add_notification($eventID, {
                                    roster_team_id: team.id,
                                    roster_club_id: team.roster_club_id,
                                    event_owner_id: eoID,
                                    division_id: $divisionID,
                                    action: 'team.division.changed',
                                    comments: $notes
                                }).catch(() => {});
                            })
                        );
                    })
                },
                Promise.resolve()
            );

            await tr.commit();
        })().then(() => {
            res.ok();
        }).catch(err => {
            if(tr && !tr.isCommited) {
                tr.rollback();
            }
            if(err.message === 'DIVISION_IS_FULL') {
                return DivisionService.checkDivisionEntrance(queryBuilderParams, $divisionID)
                    .then(message => {
                        res.status(400).json( { validation: message } );
                    }).catch(err => res.customRespError(err));
            }

            res.customRespError(err)
        });
    },

    // post /api/v2/event/:event/add-teams-manually
    addTeamsManually: function(req, res) {
        let eventID   = Number(req.params.event);
        let teamsData = req.body;

        return EventTeamService.manual_teams_addition.addTeamsManually(eventID, teamsData)
            .then(() => {
                res.status(200).json({});
            })
            .catch(res.customRespError.bind(res));
    },

    // post /api/v2/event/:event/add-roster-manually
    addRosterManually: async function (req, res) {
        let eventID = Number(req.params.event);
        let memberData = req.body;

        if(!eventID) {
            return res.validation('Event ID required');
        }

        if(_.isEmpty(memberData)) {
            return res.validation('Member required');
        }

        try {
            let {
                roster_team_id,
                master_team_id
            } = memberData;

            let member = _.omit(memberData, ['master_team_id', 'roster_team_id']);

            await EventTeamService.manual_roster_addition.createTeamMembers(master_team_id, roster_team_id, [member]);

            res.status(200).json({});
        } catch (err) {
            res.customRespError(err);
        }
    },

    // PUT /api/v2/event/:event/teams/payment-status
    updatePaymentStatus: async function(req, res) {
        try {
            const eventID = Number(req.params.event);
            const status = req.body.status;
            let teams = req.body.teams;
            const notes = req.body.notes;
            const eventOwnerID = Number(req.session.passport.user.event_owner_id);

            teams = Array.isArray(teams) ? teams : [];

            const isValidStatus = RosterTeamService.validatePaymentStatus(status);

            if (!isValidStatus) {
                throw { validation: 'Invalid Payment Status' };
            }

            if (!eventID) {
                throw { validation: 'Invalid Event Identifier' };
            }

            const queryBuilderParams = _.defaults(_.omit(req.body, 'limit', 'page', 'revert'), { event: eventID });

            await RosterTeamService.updatePaymentStatus({ status, teams, queryBuilderParams, notes, eventOwnerID });

            res.ok();
        } catch(e) {
            res.customRespError(e);
        }
    },

    // POST /api/v2/event/:event/import-teams-manually
    importTeamsManually: async function(req, res) {
        const eventID = Number(req.params.event);
        const statusEntry = Number(req.body.statusEntry);
        const file = req.file('file')._files[0];
        try {
            EventTeamService.manual_teams_addition.validateImportRequestParams(statusEntry);
            let originStream = file.stream;

            let formatFileName = function (timestamp, streamFileName, folderName) {
                let fileName = EventMediaService.formatFileName(streamFileName, timestamp);

                return folderName + '/' + fileName;
            };

            let fileName = formatFileName.bind(null, Date.now(), originStream.filename);
            const result = await EventTeamService.manual_teams_addition.importTeams(eventID, statusEntry, file, fileName);
            res.status(200).json(result);
        }
        catch(err) {
            res.customRespError(err);
        }
    },

    // POST /api/v2/event/:event/import-members-manually
    importMembersManually: async function(req, res) {
        const eventID = Number(req.params.event);
        const file = req.file('file')._files[0];

        try {
            let originStream = file.stream;

            let formatFileName = function (timestamp, streamFileName, folderName) {
                let fileName = EventMediaService.formatFileName(streamFileName, timestamp);

                return folderName + '/' + fileName;
            };

            let fileName = formatFileName.bind(null, Date.now(), originStream.filename);

            const result = await EventTeamService.manual_roster_addition.importTeams(eventID, file, fileName);

            res.status(200).json(result);
        }
        catch(err) {
            res.customRespError(err);
        }
    }
};

function changeTeamDivision (tr, eventID, division, rosterTeam) {
    let _db = tr || Db;

    let sql =
        `UPDATE "roster_team" rt 
         SET "division_id" = (
            CASE 
              WHEN (rt.age > $4::INTEGER)
                THEN rt.division_id
              ELSE $3::INTEGER
            END
         )
         WHERE rt.roster_team_id = $1
            AND rt.event_id = $2
         RETURNING rt.roster_team_id, (rt.age > $4::INTEGER) "is_greater", rt.age, rt.team_name`;

    return _db.query(sql, [rosterTeam.id, eventID, division.id, division.age])
    .then(result => {
        let updatedTeam = result.rows[0];

        if (_.isEmpty(updatedTeam)) {
            return Promise.reject({ validation: 'Team Not found' });
        }

        if (updatedTeam.is_greater) {
            return Promise.reject({
                validation:
                    `Age ${updatedTeam.age} of "${updatedTeam.team_name}" Team 
                    is greater than Division's age ${division.age}`
            });
        }
    });
}

function resultsExport (eventId, exportType, region) {
    let exportProcName = 'usav-final-report';
    let filePrefix = 'GEVA';

    if(exportType === 'GEVA') {
        exportProcName = region === 'SC' ? 'geva-report-v2': 'geva-report'
    }

    if(exportType === 'REGION-FINISHES-REPORT') {
        exportProcName = 'region-finishes-report';
        filePrefix = region === 'SC' ? 'SCVA': 'GEVA';
    }

    return Db.query(
        'SELECT e.name FROM "event" e WHERE e.event_id = $1',
        [eventId]
    ).then(result => {
        let event = _.first(result.rows);
        if(_.isEmpty(event)) {
            throw {
                validation: 'No Event Found'
            };
        }
        return event.name;
    }).then(eventName => {
        const fileName = swUtils.getSafeFileName(`${eventName} results for ${filePrefix} ${new Date().getTime()}`)

        return path.resolve(
            __dirname, '..', '..', '..', '..', 'export',
            `${fileName}.xlsx`);
    }).then(filePath => {
        return new Promise((resolve, reject) => {
            let procKiller;

            let connectionStrBase64
                    = Buffer.from(JSON.stringify(SAILS_CONNECTION)).toString('base64');

            let log         = fs.openSync(APP_LOG, 'a'),
                errorsLog   = fs.openSync(ERR_LOG, 'a');

            let xslxProc = spawn('node', [
                exportProcName,
                `--event=${eventId}`,
                `--connection=${connectionStrBase64}`,
                `--path=${filePath}`,
                `--region=${region || ''}`
            ], {
                detached    : true,
                cwd         : path.resolve(__dirname, '..', '..', '..', 'sw-utils'),
                stdio       : ['ignore', log, errorsLog]
            });

            xslxProc.on('error', err => {
                if(procKiller) { clearTimeout(procKiller); }
                reject(err);
            });

            xslxProc.on('close', code => {
                if(procKiller) { clearTimeout(procKiller); }
                if(code !== 0) {
                    reject();
                } else {
                    resolve(filePath);
                }
            });

            xslxProc.unref();

            procKiller = setTimeout(() => {
                xslxProc.kill();
            }, 10 * 1000);
        });
    });
}

function _statusEntryIcon (status) {
    var status_class = 'glyphicon ', title;
    switch(status) {
        case 11:
            title = 'Declined';
            status_class += 'glyphicon-ban-circle red';
            break;
        case 12:
            title = 'Entered';
            status_class += 'glyphicon-ok-sign green';
            break;
        case 13:
            title = 'Pending';
            status_class += 'glyphicon-plus-sign blue';
            break;
        case 14:
            title = 'Waiting';
            status_class += 'glyphicon-minus-sign yellow-waiting';
            break;
        default:
            title = 'No entered';
            status_class += 'glyphicon-remove-circle red';
            break;
    }
    return ICON_TEMPLATE.format(status_class, title, 1);
}

function _statusHousingIcon (status) {
    var status_class = 'glyphicon ', title;
    switch(status) {
        case 32:
            title = 'Verified';
            status_class += 'glyphicon-ok-sign green';
            break;
        case 33:
            title = 'Issued';
            status_class += 'glyphicon-minus-sign blue';
            break;
        case 34:
            title = 'Below';
            status_class += 'glyphicon-exclamation-sign yellow-waiting';
            break;
        case 35:
            title ='Faulty';
            status_class += 'glyphicon-ban-circle yellow-faulty';
            break;
        case 31:
        /* falls through */
        default:
            title = 'None';
            status_class += 'glyphicon-remove-circle red';
            break;
    }
    return ICON_TEMPLATE.format(status_class, title, 4);
}

function _statusPaymentIcon (status) {
    var status_class = 'glyphicon ', title;
    switch(status) {
        case 22:
            title = 'Paid';
            status_class += 'glyphicon-ok-sign green';
            break;
        case 23:
            title = 'Partial';
            status_class += 'glyphicon-minus-sign yellow-waiting';
            break;
        case 24:
            title = 'Pending';
            status_class += 'glyphicon-minus-sign blue';
            break;
        case 25:
            title = 'Refunded';
            status_class += 'glyphicon-remove violet';
            break;
        case 26:
            title = 'Disputed';
            status_class += 'glyphicon-minus-sign red';
            break;
        default:
            title = 'None';
            status_class += 'glyphicon-remove-circle red';
            break;
    }
    return ICON_TEMPLATE.format(status_class, title, 5);
}

function _statusRosterIcon (isValidRoster) {
    return ICON_TEMPLATE.format(
        `glyphicon ${isValidRoster?'glyphicon-ok-sign green':'glyphicon-remove-circle red'}`,
        `${isValidRoster?'Valid':'Invalid'} Rosters`, 2);
}

function _queryBuilder (query, params, fields, method) {
  return SQLQueryBuilder.clubs.list(query, params, fields, method);
}
