'use strict';

require('date-utils');
const updateStaffSchema = require('../validation-schemas/club-schemas').update_staff;

module.exports = {
    // get /api/master_staff
    index: async function(req, res) {
        const masterClubId = parseInt(req.session.passport.user.master_club_id, 10);
        const currentSeason = sails.config.sw_season.current;

        if (!masterClubId) {
            return res.status(403).json({ error: 'Access denied.' });
        }

        try {
            const masterStaffData = await TeamMembersService.getMasterStaffData(masterClubId, currentSeason);
            return res.status(200).json(masterStaffData);
        } catch (err) {
            return res.serverError(err);
        }
    },
    // get /api/master_staff/:master_staff
    find: function(req, res) {
        let staff_id        = parseInt(req.params.master_staff, 10),
            master_club_id  = parseInt(req.session.passport.user.master_club_id, 10),
            $currentSeason  = sails.config.sw_season.current;

        if(!staff_id) {
            return res.validation('Invalid Staff Indetifier');
        }
        if(!master_club_id) {
            return res.forbidden('No Club Found')
        }

        let sql =
            `SELECT ( 
                SELECT ROW_TO_JSON("staff") 
                FROM ( 
                    ( 
                        SELECT ms.gender, ms.organization_code, ms.first, ms.last,
                        ms.email, ms.phone, ms.phoneh, ms.city, ms.state, ms.zip,
                        ms.nick, ms.address, ms.address2, ms.phonew, ms.phoneo, ms.membership_status,
                        ms.bg_screening, ms.bg_expire_date, ms.chaperone_status, ms.coach_status,
                        ms.birthdate, ms.usav_number, ms.aau_membership_id,
                        (
                            CASE
                                WHEN (ms.safesport_statusid = '2') THEN 'OK'
                                ELSE ''
                            END
                        ) safesport_statusid,
                        (
                            CASE
                              WHEN (NULLIF(ms.cert, '') IS NOT NULL) THEN ms.cert
                              WHEN (ms.is_impact IS TRUE) THEN 'IMPACT'
                              ELSE NULL
                            END
                        ) "cert"
                    ) 
                ) "staff" 
            ) "staff", ( 
                SELECT ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("states"))) 
                FROM ( 
                    SELECT s.state, s.name 
                    FROM state s 
                    WHERE s.country = $1 
                ) "states" 
            ) "states", ( 
                SELECT ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("teams"))) 
                FROM ( 
                    SELECT mt.team_name, msr.role_id, mt.master_team_id "id", 
                            msr.primary, msr.role_id "initial_role_id" 
                    FROM master_staff_role msr
                    INNER JOIN master_team mt
                        ON mt.master_team_id = msr.master_team_id 
                        AND mt.season = $4 
                    WHERE msr.master_staff_id = ms.master_staff_id 
                    ORDER BY mt.organization_code 
                ) "teams" 
            ) "teams", (
                SELECT ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("roles"))) 
                FROM (
                    SELECT r.role_id AS id, r.name 
                    FROM ROLE r 
                    WHERE r.role_id IN (4, 5, 6, 15, 7)
                ) "roles"
            ) "roles"
            FROM master_staff ms
            WHERE ms.master_club_id = $2 
                AND ms.master_staff_id = $3`;

        Promise.all([
            Db.query(sql, ['US', master_club_id, staff_id, $currentSeason]),
            RosterSnapshotService.findBlockedEvents(master_club_id, null, staff_id)
        ]).then(results => {
            let staffData       = results[0].rows[0],
                blocked_events  = results[1];

            if(_.isEmpty(staffData)) {
                res.status(200).json({});
            } else {
                res.status(200).json(_.extend({ blocked_events }, staffData));
            }
        }).catch(err => {
            res.customRespError(err);
        });
    },

    findInTeam: function (req, res) {
        var team_id = parseInt(req.param('team'), 10);
        if(!team_id) return res.status(400).json({error: 'No team selected'});
        var master_club_id = req.session.passport.user.master_club_id;
        if(!master_club_id) return res.status(403).json({error: 'Access denied'});

        Promise.all([
            Db.query(
                `SELECT  
                    ms.gender, ms.organization_code, ms.first, ms.last, 
                    ms.email, ms.phone AS phonem, ms.phoneh, ms.city, ms.state, ms.zip,  
                    msr.role_id, ms.master_staff_id, msr.master_team_id,  
                    msr.primary, ms.usav_number, ms.aau_membership_id,
                    (
                        CASE 
                          WHEN (NULLIF(ms.cert, '') IS NOT NULL) THEN ms.cert
                          WHEN (ms.is_impact IS TRUE) THEN 'IMPACT'
                          ELSE NULL
                        END  
                    ) "cert",
                    ( 
                        SELECT mt.team_name 
                        FROM master_team mt 
                        LEFT JOIN master_staff_role msr1 ON msr1.master_team_id = mt.master_team_id 
                        WHERE msr1.master_staff_id = ms.master_staff_id 
                          AND msr1.primary = TRUE 
                        ORDER BY master_staff_role_id DESC 
                        LIMIT 1 
                    ) AS primary_team_name 
                FROM master_staff_role msr  
                LEFT JOIN master_staff ms ON msr.master_staff_id = ms.master_staff_id  
                WHERE msr.master_team_id = $1 
                  AND ms.master_club_id = $2 AND ms.deleted IS NULL`,
                [team_id, master_club_id]
            ),
            Db.query(
                `SELECT r.role_id AS id, r.name 
                 FROM role r 
                 WHERE r.role_id IN (4, 5, 6, 15)`
            )
        ]).then(results => {
            let staff = results[0].rows,
                roles = results[1].rows;

            res.status(200).json({staff, roles});
        }).catch(err => {
            res.customRespError(err);
        });
    },

    create: function(req, res) {
        if (!req.is('json')) {
            return res[400]([new Error('Request should be json')]);
        }
        var schema = createSchema({
            type: 'object',
            additionalProperties: false,
            properties: {
                birthdate: {
                    type: ['date', 'string', 'null'],
                    required: false
                },
                email: {
                    type: ['string', 'null'],
                    required: true
                },
                first: {
                    type: ['string', 'null'],
                    minLength: 1,
                    maxLength: 100,
                    required: true
                },
                last: {
                    type: ['string', 'null'],
                    minLength: 1,
                    maxLength: 100,
                    required: true
                },
                gender: {
                    type: 'string',
                    required: true,
                    enum: ['male', 'female', 'coed']
                },
                phone: {
                    type: ['string', 'null'],
                    required: true
                },
                nick: {
                    type: ['string', 'null'],
                    required: true
                },
                address2: {
                    type: ['string', 'null'],
                    required: true
                },
                phonew: {
                    type: ['string', 'null'],
                    required: true
                },
                phoneo: {
                    type: ['string', 'null'],
                    required: true
                }
            }
        });

        try {
            schema.validate(req.body);
        } catch (err) {
            return res.status(400).json(err);
        }

        var source = _.clone(req.body);
        source.master_club_id = req.session.passport.user.master_club_id;
        _.each(_.keys(source), function(key) {
            if (_.isNull(source[key])) {
                delete source[key];
            }
        });

        if(source.birthdate) {
            source.birthdate = new Date(source.birthdate);
        }

        var q = squel.insert()
            .into('master_staff')
            .setFields(source)
            .returning('*');

        Db.query(q)
        .then(result => {
            if(result.rowCount === 0) {
                res[500]([new Error('Server internal error')]);
            } else {
                res[201]({
                    master_staff: _.omit(_.first(result.rows), 'created', 'modified')
                });
            }
        }).catch(err => {
            res.customRespError(err);
        });
    },
    // put /api/master_staff/:master_staff
    update: async function(req, res) {
        const master_staff_id = req.param("master_staff");
        const master_club_id = req.session.passport.user.master_club_id;

        if(!master_staff_id) return res.status(400).json({error: 'No staff selected'});
        if(!master_club_id) return res.status(403).json({error: 'Access denied'});

        const validation = updateStaffSchema.validate(req.body);

        if(validation.error) {
            loggers.errors_log.error(validation.error);
            return res.status(400).json({ validationErrors: validation.error.details });
        }

        try {
            const updatedStaff = await TeamMembersService.updateMasterStaff(master_staff_id, master_club_id, req.body);
            return res.status(200).json({ master_staff: updatedStaff });
        } catch (err) {
            return res.customRespError(err);
        }
    },

    destroy: async function(req, res) {
        const masterStaffId = req.param('master_staff');

        if (_.isNaN(Number(masterStaffId)) || (Number(masterStaffId) <= 0)) {
            return res.customRespError({ validation: 'Master Staff Id invalid' });
        }

        var q = squel.delete()
            .from('master_staff')
            .where('master_staff_id = ?', masterStaffId)
            .returning('master_staff_id');

        Db.query(q)
        .then(result => {
            if (result.rowCount === 0) {
                res[404](new Error('Resource with that id does not exists'));
            } else {
                res[204]();
            }
        }).catch(err => {
            res.customRespError(err);
        });
    },
    //post /api/master_staff/move
    move_to_team: function (req, res) {
        var $team_id        = parseInt(req.body.team, 10),
            $staff          = req.body.staff,
            $master_club_id = parseInt(req.session.passport.user.master_club_id, 10),
            $season         = sails.config.sw_season.current;

        if(!$team_id) {
            return res.validation('No team selected');
        }
        if(!_.isArray($staff)) {
            return res.validation('Invalid staff list passed');
        }
        if ($staff.length === 0) {
            return res.validation('No staffers passed');
        }
        if(!$master_club_id) {
            return res.validation('No Club Found');
        }

        // TODO: move to service
        function insertStaffRole (tr, masterStaffID) {
            let numID = parseInt(masterStaffID, 10);

            if (!Number.isInteger(numID)) {
                return Promise.reject({ validation: 'Staff identifiers should be integers' });
            }

            return tr.query(
                `INSERT INTO "master_staff_role" (
                    master_staff_id, master_team_id, role_id
                 ) SELECT ms.master_staff_id, $3, 0 
                 FROM "master_staff" ms 
                 WHERE ms.master_staff_id = $1
                    AND ms.master_club_id = $2
                    AND ms.season = $4
                    AND ms.deleted IS NULL
                    AND NOT EXISTS ( 
                     SELECT msr.master_staff_role_id  
                     FROM "master_staff_role" msr 
                     WHERE msr.master_staff_id = ms.master_staff_id
                         AND master_team_id = $3 
                 ) RETURNING *`,
                [numID, $master_club_id, $team_id, $season]
            ).then(result => result.rows[0] || null)
        }

        // TODO: move to service
        function getMasterStaffIDFromRows (rows) {
            return rows.reduce((arr, item) => {
                if (item !== null && item.master_staff_id) {
                    arr.push(item.master_staff_id);
                }

                return arr;
            }, []);
        }


        Db.begin().then(tr => {
            const masterStaffers = [];

            return $staff.reduce((prev, id) =>
                prev.then(() =>
                    insertStaffRole(tr, id).then(staffers => {
                        masterStaffers.push(staffers);
                    })
                ), Promise.resolve()
            )
            .then(() => {

                let staffIdentifiers = getMasterStaffIDFromRows(masterStaffers);

                loggers.debug_log.verbose(
                    'Inserted', staffIdentifiers.length, '"master_staff_role" rows');

                if (staffIdentifiers.length === 0) {
                    return staffIdentifiers;
                }

                return tr.query(
                    `UPDATE "master_staff_role"
                     SET "primary" = TRUE 
                     WHERE master_staff_role_id IN ( 
                         SELECT  
                             STRING_AGG(msr.master_staff_role_id::TEXT, '')::INTEGER 
                         FROM master_staff_role msr 
                         WHERE msr.master_staff_id IN (${staffIdentifiers.join(', ')}) 
                             AND msr.master_team_id IS NOT NULL 
                         GROUP BY msr.master_staff_id 
                         HAVING COUNT(msr.master_staff_id) = 1
                     )`
                ).then(() => staffIdentifiers)
            })
            .then(processedStaffersIDs => tr.commit().then(() => processedStaffersIDs))
            .catch(err => {

                tr.rollback();

                return Promise.reject(err);
            })
        }).then(processedStaffersIDs => {
            let upd = RosterSnapshotService.upsertRosterStaffRoles.bind(RosterSnapshotService, $master_club_id);

            return processedStaffersIDs.reduce((prev, masterStaffID) =>
                prev.then(() =>
                    upd(masterStaffID)
                ),
                Promise.resolve()
            )
        }).then(() => {
            res.ok();
        })
        .catch(res.customRespError.bind(res));
    },
    // post /api/master_staff/remove
    remove_from_team: function (req, res) {
        var $staff              = req.body.staff,
            $master_club_id     = parseInt(req.session.passport.user.master_club_id, 10),
            filteredStaff       = [];

        if(!_.isArray($staff))
            return res.validation('Invalid staff list passed');
        if(!$master_club_id)
            return res.validation('No club found');

        filteredStaff = _.filter($staff, _.isNumber);

        if(!filteredStaff.length)
            return res.validation('Empty staff list passed');

        Db.query(
            `DELETE FROM "master_staff_role"
             WHERE "master_staff_id" IN ( 
                SELECT master_staff_id 
                FROM "master_staff" 
                WHERE master_staff_id IN (${
                    filteredStaff.join(', ')
                }) 
                    AND master_club_id = $1 
            )`,
            [$master_club_id]
        ).then(() => {
            return Promise.all(
                filteredStaff.map(master_staff_id =>
                    RosterSnapshotService.upsertRosterStaffRoles($master_club_id, master_staff_id)
                )
            )
        }).then(function () {
            res.ok();
        }).catch(res.customRespError.bind(res))
    },
    //delete /api/master_staff
    remove: async function (req, res) {
        const masterClubId= parseInt(req.session.passport.user.master_club_id, 10);
        const staffIds = req.body.staff;

        if (!masterClubId) return res.forbidden('Not a club owner');

        try {
            const message = await TeamMembersService.removeStaffFromClub(masterClubId, staffIds);

            return res.status(200).json({ message });
        } catch (err) {
            return res.customRespError(err);
        }
    }
};
