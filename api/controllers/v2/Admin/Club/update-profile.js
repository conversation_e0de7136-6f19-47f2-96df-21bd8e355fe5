const { clubProfileUpdate } = require("../../../../validation-schemas/admin-club");
const { CA_COUNTRY_CODE } = require("../../../../constants/common");
const { USAV_SANC_BODY } = require("../../../../constants/teams");
const ClubService = require("../../../../services/club/_ClubService");

const clubService = new ClubService();

const ALLOWED_UPDATE_FIELDS = [
    'code',
    'director_usav_code',
    'aau_club_code',
    'aau_primary_membership_id'
];

module.exports = {
    friendlyName: 'Update Club Profile',
    description: 'Update club profile with logging and sanctioning body management',

    inputs: {
        id: {
            type: 'number',
            description: 'Master Club ID',
            required: true
        }
    },

    exits: {
        success: {
            statusCode: 200,
            description: 'Successfully updated club profile'
        }
    },

    fn: async function (inputs, exits) {
        try {
            const updateData = this.req.body;

            await updateClubProfile(inputs.id, updateData);

            return exits.success({ success: true });
        } catch (error) {
            return this.res.customRespError(error);
        }
    },
};

async function updateClubProfile(clubId, updateData) {
    let tr;

    try {
        const clubData = prepareClubData(updateData);
        const validatedData = validateClubData(clubData);

        const fieldsToUpdate = getAllowedFields(validatedData);
        const hasSanctioningsToRemove = hasSanctionings(updateData.sanctionings_remove);

        if (!Object.keys(fieldsToUpdate).length && !hasSanctioningsToRemove) {
            return;
        }

        const existingClubData = await getClubProfile(clubId);
        const clubDataForValidation = mergeClubDataForValidation(existingClubData, validatedData, updateData);

        await performBusinessValidations(clubId, clubDataForValidation, existingClubData);

        tr = await Db.begin();

        await updateClub(tr, clubId, validatedData);
        await updateSanctionings(tr, clubId, updateData);

        await tr.commit();
    } catch (error) {
        if(tr && !tr.isCommited) {
            await tr.rollback();
        }
        throw error;
    }
}

function prepareClubData(updateData) {
    const editableData = {};

    ALLOWED_UPDATE_FIELDS.forEach(field => {
        if (updateData[field] !== undefined) {
            editableData[field] = updateData[field];
        }
    });

    if (updateData.usav_code !== undefined) {
        editableData.code = updateData.usav_code;
    }

    return editableData;
}

async function getClubProfile(clubId) {
    const query = knex('master_club as mc')
        .select({
            country: 'mc.country',
            region: 'mc.region',
            director_first: 'mc.director_first',
            director_last: 'mc.director_last',
            director_birthdate: knex.raw("to_char(mc.director_birthdate, 'YYYY-MM-DD')"),
            director_gender: 'mc.director_gender',
            director_usav_code: 'mc.director_usav_code',
            code: 'mc.code',
            aau_club_code: 'mc.aau_club_code',
            aau_primary_membership_id: 'mc.aau_primary_membership_id',
            aau_primary_zip: 'mc.aau_primary_zip'
        })
        .where('mc.master_club_id', clubId);

    const result = await Db.query(query);

    if (result.rows.length === 0) {
        throw { validation: 'Club not found' };
    }

    return result.rows[0];
}

function mergeClubDataForValidation(existingData, validatedData, updateData) {
    const clubData = { ...existingData, ...validatedData };

    if (hasSanctionings(updateData.current_sanctionings)) {
        clubData.has_usav_sanctioning = updateData.current_sanctionings.includes(USAV_SANC_BODY.USAV);
        clubData.has_aau_sanctioning = updateData.current_sanctionings.includes(USAV_SANC_BODY.AAU);
        clubData.need_aau_validation = existingData.aau_club_code !== clubData.aau_club_code || existingData.aau_primary_membership_id !== clubData.aau_primary_membership_id;
    }

    return clubData;
}

function validateClubData(clubData) {
    const { error, value } = clubProfileUpdate.validate(clubData);

    if (error) {
        throw { validation: error.details };
    }

    return value;
}

async function performBusinessValidations(clubId, validatedData) {
    await Promise.all([
        validateUsavDirectorIfNeeded(validatedData),
        validateAauCodeUniquenessIfNeeded(clubId, validatedData)
    ]);
}

async function validateUsavDirectorIfNeeded(clubData) {
    const shouldValidateUsav = clubData.has_usav_sanctioning &&
        clubData.director_usav_code &&
        clubData.country_code !== CA_COUNTRY_CODE;

    if (shouldValidateUsav) {
        await clubService.__validateUsavMember(clubData);
    }
}

async function validateAauCodeUniquenessIfNeeded(clubId, clubData) {
    const shouldValidateAau = clubData.has_aau_sanctioning && clubData.need_aau_validation;

    if (shouldValidateAau) {
        const codeExists = await clubService._clubAauCodeExists(
            clubData.aau_club_code,
            clubData.aau_primary_membership_id,
            clubData.aau_primary_zip
        );

        if (codeExists) {
            throw { validation: 'Club AAU code must be unique' };
        }
    }
}

async function updateClub(tr, clubId, validatedData) {
    const fieldsToUpdate = getAllowedFields(validatedData);

    if (Object.keys(fieldsToUpdate).length === 0) {
        return;
    }

    const updateResult = await executeClubUpdate(tr, clubId, fieldsToUpdate);

    if (updateResult.rows.length === 0) {
        throw { validation: 'Club not found' };
    }
}

async function updateSanctionings(tr, clubId, clubData) {
    if (hasSanctionings(clubData.sanctionings_remove)) {
        await removeSanctionings(tr, clubId, clubData.sanctionings_remove);
    }
}

function hasSanctionings(sanctionings) {
    return Array.isArray(sanctionings) && sanctionings.length > 0;
}

function getAllowedFields(data) {
    return ALLOWED_UPDATE_FIELDS.reduce((acc, field) => {
        if (data[field]) {
            acc[field] = data[field];
        }
        return acc;
    }, {});
}

async function executeClubUpdate(tr, clubId, fieldsToUpdate) {
    const query = knex('master_club')
        .where('master_club_id', clubId)
        .update(fieldsToUpdate)
        .returning('club_name');

    return tr.query(query);
}

async function removeSanctionings(tr, masterClubId, sanctionings) {
    const query = knex('master_club_sanctioning')
        .where('master_club_id', masterClubId)
        .whereIn('sport_sanctioning_id', sanctionings)
        .del();

    return tr.query(query);
}
