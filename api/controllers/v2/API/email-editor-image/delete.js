// DELETE /api/email-editor-images/v2/:id
module.exports = {
    friendlyName: 'Delete Email Editor Image',
    description: 'Soft delete Email Editor Image by marking it as deleted',

    inputs: {
        id: {
            type: 'number',
            required: true,
            description: 'The ID of the image to delete',
        },
    },

    exits: {
        success: {
            statusCode: 200,
        },
    },

    fn: async function (inputs, exits) {
        try {
            const eventOwnerIds = getEventOwnerIds(this.req);

            if (!eventOwnerIds.length) {
                throw { validation: 'Access denied' };
            }

            await EmailEditorImageService.delete({
                id: inputs.id,
                event_owner_ids: eventOwnerIds,
            });

            exits.success();
        } catch (err) {
            return this.res.customRespError(err);
        }
    },
};

function getEventOwnerIds(req) {
    const eventOwnerId = Number(req.session.passport.user.event_owner_id);
    const eventOwnerIds = eventOwnerId ? [eventOwnerId] : [];

    const sharedEventOwnerIds = req.user.shared_events && Object.values(req.user.shared_events)
        .filter(({ permissions }) => permissions.email_module_tab)
        .map(({ event_owner_id }) => event_owner_id);

    return [...eventOwnerIds, ..._.uniq(sharedEventOwnerIds)];
}
