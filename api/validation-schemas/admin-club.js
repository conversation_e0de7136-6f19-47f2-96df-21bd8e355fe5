const Joi = require('joi');
const { EMAIL_REGEX, USAV_REG_EXP } = require('../lib/joi-constants');

const customRegexMsg = function (msg) {
    msg = msg || 'is Invalid';
    return {
        messages: {
            'string.regex': {
                base: msg
            }
        }
    };
};

const clubSearch = Joi.object().keys({
    usav_code: Joi.string()
        .allow(null, "")
        .truncate()
        .optional()
        .pattern(/^(\d|\w)+$/)
        .preferences(customRegexMsg("should contain only numbers or/and letters"))
        .max(5)
        .label("Club USAV Code"),
    director_usav_code: Joi.string()
        .allow(null, "")
        .truncate()
        .optional()
        .pattern(USAV_REG_EXP)
        .preferences(customRegexMsg())
        .label("USAV Club Director Membership ID"),
    aau_code: Joi.string()
        .allow(null, "")
        .truncate()
        .optional()
        .label("Club AAU Code"),
    aau_primary_membership_id: Joi.string()
        .allow(null, "")
        .truncate()
        .optional()
        .label("AAU Representative Membership ID"),
    director_email: Joi.string()
        .allow(null, "")
        .truncate()
        .optional()
        .pattern(EMAIL_REGEX)
        .label("Club Director Email"),
}).or("usav_code", "director_usav_code", "aau_code", "aau_primary_membership_id", "director_email")
    .messages({
        "object.missing": "Please enter at least one search value to proceed."
    });

const clubProfileUpdate = Joi.object().keys({
    code: Joi.string()
        .allow(null, "")
        .pattern(/^(\d|\w)+$/)
        .preferences(customRegexMsg('should contain only numbers or/and letters'))
        .max(5)
        .label('Club USAV Code'),
    director_usav_code: Joi.string()
        .allow(null, "")
        .pattern(USAV_REG_EXP)
        .preferences(customRegexMsg())
        .label('USAV Club Director Membership ID'),
    aau_club_code: Joi.string()
        .allow(null, "")
        .label('Club AAU Code'),
    aau_primary_membership_id: Joi.string()
        .allow(null, "")
        .label('AAU Representative Membership Id'),
});

module.exports = {
    clubSearch,
    clubProfileUpdate,
};
