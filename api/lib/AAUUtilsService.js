const moment = require('moment');
const {
    CANONICAL_MEMBERSHIP_TYPE, 
    SOAP_MEMBERSHIP_TYPE,
    REST_MEMBERSHIP_TOKENS,
    REST_GENDER_ALIASES,
    SOAP_GENDER,
    CANONIC<PERSON>_GENDER,
    APP_GENDER,
    DATE_FORMATS,
} = require('../constants/aau/member');


class AAUUtilsService {
    get VALID_BACKGROUND_SCREENING_STATUS () {
        return 2;
    }

    get INVALID_BACKGROUND_SCREENING_STATUS () {
        return 1;
    }

    get MIN_AAU_AGE () {
        return 8;
    }

    get PROFILE_COMPLETED_FIELD () {
        return 'aau_profile_completed_at';
    }

    get AAU_FIELDS () {
        return {
            CLUB_CODE: 'club_code',
            MEMBERSHIP_IDENTIFIER: 'membership_identifier',
            ZIP_CODE: 'zip_code',
            LAST_NAME: 'last_name',
            BIRTH_DATE: 'birth_date',
            FIRST_NAME: 'first_name',
        }
    }

    // AAU SOAP provider member field names (preferred name)
    get SOAP_MEMBER_FIELDS() {
        return {
            CATEGORY_CODE: 'CategoryCode',
            ADDRESS: 'AddressLine1',
            ADDRESS2: 'AddressLine2',
            CITY: 'City',
            COUNTRY: 'Country',
            STATE: 'State',
            ZIP_CODE: 'ZipCode',
            DISTRICT: 'AssociationCode',
            BIRTHDATE: 'DateOfBirth',
            EMAIL: 'EmailAddress',
            FIRST: 'FirstName',
            LAST: 'LastName',
            GENDER: 'Gender',
            GRAD_YEAR: 'GraduationYear',
            AAU_MEMBERSHIP_ID: 'MembershipID',
            AAU_MEMBERSHIP_ENDING_YEAR: 'MembershipTermEndingYear',
            PHONEM: 'Phone',
            CLUB_CODE: 'ClubCode',
        }
    }

    get MEMBER_TYPE () {
        return {
            ATHLETE: 'athlete',
            STAFF: 'staff',
        }
    }

    get CATEGORY_CODE () {
        return {
            A: 'athlete',
            N: 'staff',
        }
    }

    get GENDER () {
        return {
            M: 'male',
            F: 'female',
            NB: 'non-binary',
        }
    }

    get IMPORT_MODE () {
        return {
            DEFAULT: 'default',
            INSERT: 'insert'
        }
    }

    getMinAge (birthday) {
        if(!birthday) {
           return null;
        }

        const currentSeason = sails.config.sw_season.current;
        const bday = moment(birthday, DATE_FORMATS.ISO_DATE);

        let AAUAge = currentSeason - bday.year();
        let seasonStart = moment().year(currentSeason).month(7 - 1).date(0);
        let birthdateInCurrentSeason = bday.clone().set('year', currentSeason);

        if (birthdateInCurrentSeason.isAfter(seasonStart)) {
            AAUAge--;
        }

        if (AAUAge < this.MIN_AAU_AGE) {
            AAUAge = this.MIN_AAU_AGE;
        }

        return AAUAge;
    }

    getSeasonEndByYear (year) {
        return moment().year(year).month('September').date(0).format(DATE_FORMATS.ISO_DATE);
    }

    // Map legacy AAU SOAP member object -> Canonical Member shape
    mapSoapDtoToCanonical(member = {}) {
        if (!member || _.isEmpty(member)) return {};

        const F = this.SOAP_MEMBER_FIELDS;
        const genderCode = member[F.GENDER]; // M|F|NB
        const gender = this.__toCanonicalGenderFromSoap(genderCode);

        const birthDateRaw = member[F.BIRTHDATE];
        const birthDate = birthDateRaw
            ? moment(birthDateRaw).format(DATE_FORMATS.ISO_DATE)
            : undefined;

        const endYear = member[F.AAU_MEMBERSHIP_ENDING_YEAR]
            ? Number(member[F.AAU_MEMBERSHIP_ENDING_YEAR])
            : undefined;

        const endDate = endYear ? this.getSeasonEndByYear(endYear) : undefined;

        return {
            membershipNumber: member[F.AAU_MEMBERSHIP_ID],
            clubMembershipNumber: member[F.CLUB_CODE],

            firstName: member[F.FIRST],
            lastName: member[F.LAST],
            gender,
            birthDate,

            email: member[F.EMAIL],
            address: member[F.ADDRESS],
            address2: member[F.ADDRESS2],
            city: member[F.CITY],
            stateCode: member[F.STATE],
            countryCode: member[F.COUNTRY],
            zipCode: member[F.ZIP_CODE],
            phone: member[F.PHONEM] ? String(member[F.PHONEM]).replace(/\D/g, '') : undefined,

            graduationYear: member[F.GRAD_YEAR] ? Number(member[F.GRAD_YEAR]) : undefined,
            districtCode: member[F.DISTRICT],

            membershipType: this.__toCanonicalMembershipTypeFromSoap(member[F.CATEGORY_CODE]),
            endDate,
            endYear,
            // Convenience derived
            age: this.getMinAge(birthDate),
        };
    }

    // Map AAU REST DTO -> Canonical Member shape (fields per docs)
    mapRestDtoToCanonical(dto = {}) {
        if (!dto || _.isEmpty(dto)) return {};

        const person = dto.person || {};
        const state = person.state || {};
        const country = person.country || {};

        const birthDateRaw = person.birthDate || dto.birthDate;
        const birthDate = birthDateRaw ? moment(birthDateRaw).format(DATE_FORMATS.ISO_DATE) : undefined;

        const endDateRaw = dto.endDate || dto.membershipEndDate;
        const endDate = endDateRaw ? moment(endDateRaw).format() : undefined;
        const endYear = endDateRaw ? moment(endDateRaw).year() : undefined;

        const gender = this.__toCanonicalGenderFromRest(person.gender || dto.gender);

        return {
            membershipNumber: dto.membershipNumber || dto.membershipId,
            clubMembershipNumber: dto.clubMembershipNumber || dto.clubMembershipId,

            firstName: person.firstName || dto.firstName,
            lastName: person.lastName || dto.lastName,
            gender,
            birthDate,

            email: person.email || dto.email,
            address: person.address || dto.address || dto.addressLine1,
            address2: person.address2 || dto.addressLine2,
            city: person.city || dto.city,
            stateCode: state.code || person.state || dto.stateCode,
            countryCode: country.code || person.country || dto.countryCode,
            zipCode: person.zipCode || dto.zipCode,
            phone: (person.phoneNumber || dto.phone) ? String(person.phoneNumber || dto.phone).replace(/\D/g, '') : undefined,

            graduationYear: person.graduationYear ? Number(person.graduationYear) : undefined,
            districtCode: dto.districtCode,

            membershipType: this.__toCanonicalMembershipTypeFromRest(dto.membershipType),
            startDate: dto.startDate,
            endDate,
            endYear,
            age: this.getMinAge(birthDate),
        };
    }

    mapCanonicalGenderToApp(gender) {
        switch ((gender || '').toUpperCase()) {
            case CANONICAL_GENDER.MALE: return APP_GENDER.MALE;
            case CANONICAL_GENDER.FEMALE: return APP_GENDER.FEMALE;
            case CANONICAL_GENDER.OTHER: return APP_GENDER.OTHER;
            default: return null;
        }
    }

    __toCanonicalGenderFromSoap(code) {
        switch ((code || '').toUpperCase()) {
            case SOAP_GENDER.MALE: return CANONICAL_GENDER.MALE;
            case SOAP_GENDER.FEMALE: return CANONICAL_GENDER.FEMALE;
            case SOAP_GENDER.OTHER: return CANONICAL_GENDER.OTHER;
            default: return CANONICAL_GENDER.UNKNOWN;
        }
    }

    __toCanonicalGenderFromRest(value) {
        const valueUpper = (value || '').toUpperCase();

        if ([CANONICAL_GENDER.MALE, CANONICAL_GENDER.FEMALE, CANONICAL_GENDER.OTHER].includes(valueUpper))
            return valueUpper;

        if (valueUpper === REST_GENDER_ALIASES.NON_BINARY || valueUpper === REST_GENDER_ALIASES.NON_BINARY_DASH)
            return CANONICAL_GENDER.OTHER;
        return CANONICAL_GENDER.UNKNOWN;
    }

    __toCanonicalMembershipTypeFromSoap(code) {
        const codeUpper = (code || '').toUpperCase();

        if (codeUpper === SOAP_MEMBERSHIP_TYPE.ATHLETE)
            return CANONICAL_MEMBERSHIP_TYPE.ATHLETE;

        if (codeUpper === SOAP_MEMBERSHIP_TYPE.NON_ATHLETE)
            return CANONICAL_MEMBERSHIP_TYPE.NON_ATHLETE;
        return CANONICAL_MEMBERSHIP_TYPE.OTHER;
    }

    __toCanonicalMembershipTypeFromRest(value) {
        const valueUpper = (value || '').toUpperCase();

        if (valueUpper.includes(REST_MEMBERSHIP_TOKENS.ATHLETE) && !valueUpper.includes(REST_MEMBERSHIP_TOKENS.NON))
            return CANONICAL_MEMBERSHIP_TYPE.ATHLETE;

        if (valueUpper.includes(REST_MEMBERSHIP_TOKENS.NON) || valueUpper.includes(REST_MEMBERSHIP_TOKENS.ADULT))
            return CANONICAL_MEMBERSHIP_TYPE.NON_ATHLETE;

        return CANONICAL_MEMBERSHIP_TYPE.OTHER;
    }
}

module.exports = new AAUUtilsService();
