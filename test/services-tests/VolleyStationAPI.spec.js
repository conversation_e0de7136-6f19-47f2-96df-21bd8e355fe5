'use strict';

const crypto = require('crypto');

describe('Volley Station', function () {

    // Policies
    let authPolicy;
    let rateLimitReadPolicy;
    let rateLimitWritePolicy;
    let eventAccessPolicy;

    // Keep original references to restore
    let originalVolleyStationConfig;
    let hadOriginalVolleyStationConfig = false;
    let originalRateLimiterService;
    let hadOriginalRateLimiterService = false;
    let originalVolleyStationService;
    let hadOriginalVolleyStationService = false;

    // Test configuration
    const TEST_API_KEY = 'test-volley-station-api-key-123';
    const TEST_RATE_LIMIT_CONFIG = {
        read: { points: 100, duration: 60, blockDuration: 300 },
        write: { points: 50, duration: 60, blockDuration: 600 }
    };

    before(function () {
        if (typeof global.sails.config.volleyStation !== 'undefined') {
            originalVolleyStationConfig = JSON.parse(JSON.stringify(global.sails.config.volleyStation));
            hadOriginalVolleyStationConfig = true;
        }
        if (typeof global.RateLimiterService !== 'undefined') {
            originalRateLimiterService = global.RateLimiterService;
            hadOriginalRateLimiterService = true;
        }
        if (typeof global.VolleyStationService !== 'undefined') {
            originalVolleyStationService = global.VolleyStationService;
            hadOriginalVolleyStationService = true;
        }

        if (!global.sails.config.volleyStation) {
            global.sails.config.volleyStation = {};
        }
        global.sails.config.volleyStation.apiKey = TEST_API_KEY;
        global.sails.config.volleyStation.rateLimit = JSON.parse(JSON.stringify(TEST_RATE_LIMIT_CONFIG));

        // Stub an in-memory RateLimiterService that behaves like middleware:
        // createRateLimiter(name, config, keySelector) => (req, res, next) => next()
        global.RateLimiterService = {
            createRateLimiter: sinon.stub().callsFake((_name, _config, _keySelector) => {
                // Synchronous next() call to avoid race conditions in tests where policy
                // might not await the limiter promise.
                const limiter = sinon.stub().callsFake((req, res, next) => {
                    if (res && typeof res.set === 'function') {
                        res.set('X-RateLimit-Limit', String(_config.points));
                    }
                    if (typeof next === 'function') next();
                });
                return limiter;
            })
        };

        // Stub VolleyStationService for event access policy
        global.VolleyStationService = {
            isEnabledForEvent: sinon.stub()
        };

        // Load policies after stubbing services
        authPolicy = require('../../api/policies/volley-station/auth');
        rateLimitReadPolicy = require('../../api/policies/volley-station/rateLimitRead');
        rateLimitWritePolicy = require('../../api/policies/volley-station/rateLimitWrite');
        eventAccessPolicy = require('../../api/policies/volley-station/eventAccess');
    });

    after(function () {
        // Restore volleyStation config
        if (hadOriginalVolleyStationConfig) {
            global.sails.config.volleyStation = originalVolleyStationConfig;
        } else {
            delete global.sails.config.volleyStation;
        }

        // Restore RateLimiterService
        if (hadOriginalRateLimiterService) {
            global.RateLimiterService = originalRateLimiterService;
        } else {
            delete global.RateLimiterService;
        }

        // Restore VolleyStationService
        if (hadOriginalVolleyStationService) {
            global.VolleyStationService = originalVolleyStationService;
        } else {
            delete global.VolleyStationService;
        }
    });

    beforeEach(function () {
        // Reset cached limiter instances if the policy caches them
        if (rateLimitReadPolicy && rateLimitReadPolicy.rateLimiter) {
            delete rateLimitReadPolicy.rateLimiter;
        }
        if (rateLimitWritePolicy && rateLimitWritePolicy.rateLimiter) {
            delete rateLimitWritePolicy.rateLimiter;
        }

        // Reset stubs’ history
        if (global.RateLimiterService?.createRateLimiter?.resetHistory) {
            global.RateLimiterService.createRateLimiter.resetHistory();
        }
        if (global.VolleyStationService?.isEnabledForEvent?.resetHistory) {
            global.VolleyStationService.isEnabledForEvent.resetHistory();
        }
    });

    describe('Authentication Policy', function () {

        it('should authenticate valid API key and assign deterministic clientId', function () {
            const mockReq = {
                get: sinon.stub().returns(TEST_API_KEY),
                user: {}
            };
            const mockRes = {
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            const mockNext = sinon.stub();

            authPolicy(mockReq, mockRes, mockNext);

            expect(mockNext.calledOnce).to.be.true;
            expect(mockRes.status.called).to.be.false;
            expect(mockRes.json.called).to.be.false;

            const expectedClientId = crypto
                .createHash('sha256')
                .update(TEST_API_KEY)
                .digest('hex')
                .slice(0, 8);

            expect(mockReq.user.clientId).to.equal(expectedClientId);
        });

        it('should reject missing authorization token with 401', function () {
            const mockReq = {
                get: sinon.stub().returns(undefined),
                user: {}
            };
            const mockRes = {
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            const mockNext = sinon.stub();

            authPolicy(mockReq, mockRes, mockNext);

            expect(mockNext.called).to.be.false;
            expect(mockRes.status.calledWith(401)).to.be.true;
            expect(mockRes.json.calledOnce).to.be.true;

            const response = mockRes.json.firstCall.args[0];
            expect(response).to.be.an('object');
            expect(response.message).to.match(/Authorization token is missing/i);
        });

        it('should reject invalid API key with 401', function () {
            const mockReq = {
                get: sinon.stub().returns('some-invalid-key'),
                user: {}
            };
            const mockRes = {
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            const mockNext = sinon.stub();

            authPolicy(mockReq, mockRes, mockNext);

            expect(mockNext.called).to.be.false;
            expect(mockRes.status.calledWith(401)).to.be.true;
            expect(mockRes.json.calledOnce).to.be.true;

            const response = mockRes.json.firstCall.args[0];
            expect(response).to.be.an('object');
            expect(response.message).to.match(/Authorization token invalid/i);
        });

        it('should preserve existing req.user properties', function () {
            const mockReq = {
                get: sinon.stub().returns(TEST_API_KEY),
                user: { existingProperty: 'keep-me' }
            };
            const mockRes = {
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            const mockNext = sinon.stub();

            authPolicy(mockReq, mockRes, mockNext);

            expect(mockNext.calledOnce).to.be.true;
            expect(mockReq.user.existingProperty).to.equal('keep-me');
            expect(mockReq.user.clientId).to.be.a('string');
        });
    });

    describe('Rate Limiting Policies', function () {

        it('rateLimitRead: should create rate limiter with correct name, config, and key selector', async function () {
            const mockReq = {
                method: 'GET',
                path: '/api/volley-station/v1/events/123/schedule',
                user: { clientId: 'read-client-id' }
            };
            const mockRes = {
                set: sinon.stub(),
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            const mockNext = sinon.stub();

            await rateLimitReadPolicy(mockReq, mockRes, mockNext);

            expect(global.RateLimiterService.createRateLimiter.calledOnce).to.be.true;

            const [name, config, keySelector] = global.RateLimiterService.createRateLimiter.firstCall.args;
            expect(name).to.equal('volley-station:read');
            expect(config).to.deep.equal(global.sails.config.volleyStation.rateLimit.read);
            expect(keySelector).to.be.a('function');
            expect(keySelector(mockReq)).to.equal('read-client-id');

            expect(mockNext.calledOnce).to.be.true;
        });

        it('rateLimitRead: should reuse cached limiter instance on subsequent calls', async function () {
            const mockReq = {
                method: 'GET',
                path: '/api/volley-station/v1/events/123/schedule',
                user: { clientId: 'cached-id' }
            };
            const mockRes = {
                set: sinon.stub(),
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            const mockNext = sinon.stub();

            await rateLimitReadPolicy(mockReq, mockRes, mockNext);
            expect(global.RateLimiterService.createRateLimiter.calledOnce).to.be.true;

            await rateLimitReadPolicy(mockReq, mockRes, mockNext);
            expect(global.RateLimiterService.createRateLimiter.calledOnce).to.be.true; // still 1

            expect(!!rateLimitReadPolicy.rateLimiter).to.be.true;
            expect(mockNext.callCount).to.equal(2);
        });

        it('rateLimitWrite: should create write limiter with correct name and config', async function () {
            const mockReq = {
                method: 'POST',
                path: '/api/volley-station/v1/events/123/some-write',
                user: { clientId: 'write-client-id' }
            };
            const mockRes = {
                set: sinon.stub(),
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            const mockNext = sinon.stub();

            await rateLimitWritePolicy(mockReq, mockRes, mockNext);

            expect(global.RateLimiterService.createRateLimiter.calledOnce).to.be.true;

            const [name, config, keySelector] = global.RateLimiterService.createRateLimiter.firstCall.args;
            expect(name).to.equal('volley-station:write');
            expect(config).to.deep.equal(global.sails.config.volleyStation.rateLimit.write);
            expect(keySelector).to.be.a('function');
            expect(keySelector(mockReq)).to.equal('write-client-id');

            expect(mockNext.calledOnce).to.be.true;
        });
    });

    describe('Event Access Policy', function () {

        it('should allow access when service enables event', async function () {
            global.VolleyStationService.isEnabledForEvent.resolves(true);

            const mockReq = { params: { eventId: '123' } };
            const mockRes = {
                validation: sinon.stub(),
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            const mockNext = sinon.stub();

            await eventAccessPolicy(mockReq, mockRes, mockNext);

            expect(global.VolleyStationService.isEnabledForEvent.calledWith(123)).to.be.true;
            expect(mockNext.calledOnce).to.be.true;
            expect(mockRes.status.called).to.be.false;
            expect(mockRes.json.called).to.be.false;
        });

        it('should deny access with 403 when service disables event', async function () {
            global.VolleyStationService.isEnabledForEvent.resolves(false);

            const mockReq = { params: { eventId: '456' } };
            const mockRes = {
                validation: sinon.stub(),
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            const mockNext = sinon.stub();

            await eventAccessPolicy(mockReq, mockRes, mockNext);

            expect(global.VolleyStationService.isEnabledForEvent.calledWith(456)).to.be.true;
            expect(mockNext.called).to.be.false;
            expect(mockRes.status.calledWith(403)).to.be.true;
            expect(mockRes.json.calledOnce).to.be.true;

            const body = mockRes.json.firstCall.args[0];
            expect(body && body.message).to.match(/Event not available for VolleyStation integration/i);
        });

        it('should reject invalid event IDs (non-numeric) via validation helper', async function () {
            const mockReq = { params: { eventId: 'invalid' } };
            const mockRes = {
                validation: sinon.stub(),
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            const mockNext = sinon.stub();

            await eventAccessPolicy(mockReq, mockRes, mockNext);

            expect(global.VolleyStationService.isEnabledForEvent.called).to.be.false;
            expect(mockNext.called).to.be.false;
            expect(mockRes.validation.calledWith('Invalid event identifier passed')).to.be.true;
        });

        it('should reject missing event ID via validation helper', async function () {
            const mockReq = { params: {} };
            const mockRes = {
                validation: sinon.stub(),
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            const mockNext = sinon.stub();

            await eventAccessPolicy(mockReq, mockRes, mockNext);

            expect(global.VolleyStationService.isEnabledForEvent.called).to.be.false;
            expect(mockNext.called).to.be.false;
            expect(mockRes.validation.calledWith('Invalid event identifier passed')).to.be.true;
        });

        it('should return 500 when service throws', async function () {
            global.VolleyStationService.isEnabledForEvent.rejects(new Error('Database error'));

            const mockReq = { params: { eventId: '789' } };
            const mockRes = {
                validation: sinon.stub(),
                status: sinon.stub().returnsThis(),
                json: sinon.stub()
            };
            const mockNext = sinon.stub();

            await eventAccessPolicy(mockReq, mockRes, mockNext);

            expect(global.VolleyStationService.isEnabledForEvent.calledWith(789)).to.be.true;
            expect(mockNext.called).to.be.false;
            expect(mockRes.status.calledWith(500)).to.be.true;
            expect(mockRes.json.calledOnce).to.be.true;

            const body = mockRes.json.firstCall.args[0];
            expect(body && body.message).to.match(/Unable to verify event access/i);
        });
    });
});
