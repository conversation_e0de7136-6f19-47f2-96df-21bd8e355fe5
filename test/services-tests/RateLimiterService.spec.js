'use strict';

describe('RateLimiterService (unit)', function () {
    let RateLimiterService;
    let sandbox;

    // Simple req/res/next factory
    function makeReqResNext(overrides = {}) {
        const req = Object.assign(
            { method: 'GET', path: '/test', user: { clientId: 'test-client' } },
            overrides.req || {}
        );
        const res = Object.assign(
            { set: sinon.stub(), status: sinon.stub().returnsThis(), json: sinon.stub() },
            overrides.res || {}
        );
        const next = sinon.stub();
        return { req, res, next };
    }

    before(function () {
        RateLimiterService = require('../../api/services/RateLimiterService');
    });

    beforeEach(function () {
        sandbox = sinon.createSandbox();
        // Reset service internal state between tests
        RateLimiterService._client = null;
        RateLimiterService._initPromise = null;
    });

    afterEach(function () {
        sandbox.restore();
        RateLimiterService._client = null;
        RateLimiterService._initPromise = null;
    });

    context('createRateLimiter() basics', function () {

        it('returns a middleware function', function () {
            const mw = RateLimiterService.createRateLimiter(
                'ns',
                { points: 10, duration: 60 },
                req => req?.user?.clientId
            );
            expect(typeof mw).to.equal('function');
        });

        it('skips OPTIONS requests', async function () {
            const ctx = makeReqResNext({ req: { method: 'OPTIONS' } });
            // Ensure we never try to init client
            sandbox.stub(RateLimiterService, '_initClient').resolves(false);

            const mw = RateLimiterService.createRateLimiter(
                'ns',
                { points: 10, duration: 60 },
                r => r?.user?.clientId
            );
            await mw(ctx.req, ctx.res, ctx.next);

            expect(ctx.next.calledOnce).to.be.true;
            expect(ctx.res.set.called).to.be.false;
            expect(ctx.res.status.called).to.be.false;
        });

        it('skips when key selector returns null/undefined/empty', async function () {
            const ksVariants = [() => null, () => undefined, () => ''];
            sandbox.stub(RateLimiterService, '_initClient').resolves(false);

            for (const ks of ksVariants) {
                const ctx = makeReqResNext();
                const mw = RateLimiterService.createRateLimiter('ns', { points: 10, duration: 60 }, ks);
                await mw(ctx.req, ctx.res, ctx.next);

                expect(ctx.next.calledOnce).to.be.true;
                expect(ctx.res.set.called).to.be.false;
                expect(ctx.res.status.called).to.be.false;
            }
        });

        it('handles async key selector', async function () {
            sandbox.stub(RateLimiterService, '_initClient').resolves(false);

            const keySelector = async r => {
                await new Promise(resolve => setTimeout(resolve, 5));
                return r?.user?.clientId;
            };

            const ctx = makeReqResNext();
            const mw = RateLimiterService.createRateLimiter('ns', { points: 10, duration: 60 }, keySelector);
            await mw(ctx.req, ctx.res, ctx.next);

            expect(ctx.next.calledOnce).to.be.true;
            expect(ctx.res.set.called).to.be.false;
            expect(ctx.res.status.called).to.be.false;
        });
    });

    context('_softTimeout()', function () {

        it('resolves with promise result if completed within timeout', async function () {
            const fast = Promise.resolve('ok');
            const result = await RateLimiterService._softTimeout(fast, 1000);
            expect(result).to.equal('ok');
        });

        it('resolves with timeout flag if promise takes too long (fake timers)', async function () {
            const clock = sandbox.useFakeTimers();

            const slow = new Promise(resolve => setTimeout(() => resolve('slow'), 100));
            const retP = RateLimiterService._softTimeout(slow, 50);

            clock.tick(60);
            const result = await retP;
            expect(result).to.equal('__RL_TIMEOUT__');

            // Let the slow promise settle to avoid dangling timers in the fake clock
            clock.tick(100);
        });
    });

    context('_getKeySelectorSafe()', function () {

        it('returns key selector result on success', async function () {
            const req = { user: { id: 'u-1' } };
            const ks = async r => r.user.id;
            const result = await RateLimiterService._getKeySelectorSafe(ks, req);
            expect(result).to.equal('u-1');
        });

        it('returns null on key selector error', async function () {
            const ks = async () => { throw new Error('boom'); };
            const result = await RateLimiterService._getKeySelectorSafe(ks, {});
            expect(result).to.equal(null);
        });
    });

    context('Fail-open strategy (no real Redis)', function () {

        it('proceeds when initialization fails', async function () {
            sandbox.stub(RateLimiterService, '_initClient').resolves(false);

            const ctx = makeReqResNext();
            const mw = RateLimiterService.createRateLimiter('ns', { points: 10, duration: 60 }, r => r?.user?.clientId);
            await mw(ctx.req, ctx.res, ctx.next);

            expect(ctx.next.calledOnce).to.be.true;
            expect(ctx.res.status.called).to.be.false;
        });

        it('proceeds when client is not ready', async function () {
            // Pretend init succeeded but client not ready
            sandbox.stub(RateLimiterService, '_initClient').resolves(true);
            RateLimiterService._client = { status: 'connecting' };

            const ctx = makeReqResNext();
            const mw = RateLimiterService.createRateLimiter('ns', { points: 10, duration: 60 }, r => r?.user?.clientId);
            await mw(ctx.req, ctx.res, ctx.next);

            expect(ctx.next.calledOnce).to.be.true;
            expect(ctx.res.status.called).to.be.false;
            expect(ctx.res.set.called).to.be.false;
        });

        it('proceeds on soft-timeout of limiter call', async function () {
            // Client is ready, limiter exists, but soft timeout triggers
            sandbox.stub(RateLimiterService, '_initClient').resolves(true);
            RateLimiterService._client = { status: 'ready' };

            // Whatever promise we pass, force soft-timeout to resolve to "__RL_TIMEOUT__"
            sandbox.stub(RateLimiterService, '_softTimeout').resolves('__RL_TIMEOUT__');

            // Provide a limiter with a consume that would normally resolve someday
            const limiter = { consume: sinon.stub().returns(new Promise(() => {})) }; // never-resolving
            sandbox.stub(RateLimiterService, '_createRateLimiter').returns(limiter);

            const ctx = makeReqResNext();
            const mw = RateLimiterService.createRateLimiter('ns', { points: 10, duration: 60, softTimeoutMs: 50 }, r => r?.user?.clientId);
            await mw(ctx.req, ctx.res, ctx.next);

            expect(ctx.next.calledOnce).to.be.true;
            expect(ctx.res.status.called).to.be.false;
        });
    });

    context('Limiter flow (stubbed limiter, no Redis)', function () {

        it('sets rate limit headers on successful request', async function () {
            sandbox.stub(RateLimiterService, '_initClient').resolves(true);
            RateLimiterService._client = { status: 'ready' };

            const config = { points: 5, duration: 60 };
            const limiter = {
                consume: sinon.stub().resolves({ remainingPoints: 4, msBeforeNext: 10000 })
            };
            sandbox.stub(RateLimiterService, '_createRateLimiter').returns(limiter);

            const ctx = makeReqResNext();
            const mw = RateLimiterService.createRateLimiter('rl:headers', config, r => r?.user?.clientId);

            await mw(ctx.req, ctx.res, ctx.next);

            expect(ctx.next.calledOnce).to.be.true;
            expect(ctx.res.set.calledOnce).to.be.true;

            const headers = ctx.res.set.firstCall.args[0];
            expect(headers).to.have.property('RateLimit-Limit', String(config.points));
            expect(headers).to.have.property('RateLimit-Remaining');
            expect(headers).to.have.property('RateLimit-Reset');
        });

        it('returns 429 with proper payload when rate limit exceeded', async function () {
            sandbox.stub(RateLimiterService, '_initClient').resolves(true);
            RateLimiterService._client = { status: 'ready' };

            const err = new Error('Rate limit exceeded');
            err.remainingPoints = 0;
            err.msBeforeNext = 30000;

            const limiter = {
                consume: sinon.stub().rejects(err)
            };
            sandbox.stub(RateLimiterService, '_createRateLimiter').returns(limiter);

            const ctx = makeReqResNext();
            const mw = RateLimiterService.createRateLimiter('rl:error', { points: 1, duration: 60 }, r => r?.user?.clientId);

            await mw(ctx.req, ctx.res, ctx.next);

            expect(ctx.next.called).to.be.false;
            expect(ctx.res.status.calledWith(429)).to.be.true;
            expect(ctx.res.json.calledOnce).to.be.true;

            const payload = ctx.res.json.firstCall.args[0];
            expect(payload).to.have.property('error', 'rate_limited');
            expect(payload).to.have.property('rateLimiter', 'rl:error');
            expect(payload).to.have.property('retryAfterMs', 30000);
        });

        it('creates limiter only once per middleware instance (caching)', async function () {
            sandbox.stub(RateLimiterService, '_initClient').resolves(true);
            RateLimiterService._client = { status: 'ready' };

            const limiter = { consume: sinon.stub().resolves({ remainingPoints: 9, msBeforeNext: 1000 }) };
            const createSpy = sandbox.stub(RateLimiterService, '_createRateLimiter').returns(limiter);

            const ctx = makeReqResNext();
            const mw = RateLimiterService.createRateLimiter('rl:cache', { points: 10, duration: 60 }, r => r?.user?.clientId);

            await mw(ctx.req, ctx.res, ctx.next);
            await mw(ctx.req, ctx.res, ctx.next);

            expect(createSpy.calledOnce).to.be.true; // cached after first call
        });

        it('passes relevant config through to limiter factory', async function () {
            sandbox.stub(RateLimiterService, '_initClient').resolves(true);
            RateLimiterService._client = { status: 'ready' };

            const limiter = { consume: sinon.stub().resolves({ remainingPoints: 0, msBeforeNext: 500 }) };
            const createSpy = sandbox.stub(RateLimiterService, '_createRateLimiter').returns(limiter);

            const cfg = { points: 3, duration: 60, blockDuration: 300, execEvenly: false };
            const mw = RateLimiterService.createRateLimiter('rl:config', cfg, r => r?.user?.clientId);

            const ctx = makeReqResNext();
            await mw(ctx.req, ctx.res, ctx.next);

            expect(createSpy.calledOnce).to.be.true;
            const passedCfg = createSpy.firstCall.args[1];
            expect(passedCfg.points).to.equal(3);
            expect(passedCfg.duration).to.equal(60);
            expect(passedCfg.blockDuration).to.equal(300);
            expect(passedCfg.execEvenly).to.equal(false);
        });
    });
});
