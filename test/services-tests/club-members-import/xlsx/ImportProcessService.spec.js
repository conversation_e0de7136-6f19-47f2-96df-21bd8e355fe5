const ClubPreparationHelper = require('../utils/club-preparation.helper');
const XlsxImportHelper = require('../utils/xlsx-import.helper');
const XlsxResultsHelper = require('../utils/xlsx-results-check.helper');
const AAUUtils = require('../../../../api/lib/AAUUtilsService');

const AAUImportHelper = require('../utils/aau-import.helper');
const SEImportHelper = require('../utils/se-import.helper');
const AAUResultsHelper = require('../utils/aau-results-check.helper');
const SEResultsHelper = require('../utils/se-results-check.helper');

/*
* Testing flows
* 1. Import from XLSX when there are no members in club.
* 2. Import from XLSX when there are members imported from XLSX file.
* 3. Import from XLSX after import from AAU
* 4. Import from XLSX after import from USAV/SE
*/

describe('XLSX Club Members Import', () => {
    let AAUImportService;
    let SEImportService;
    let XLSXImportService;
    let RosterSnapshotServiceClubSnapshotStub;
    let AAUServiceGetMembersStub;
    let SEServiceGetMembersStub;
    let AAUService;
    let SportsEngineService;
    let RosterSnapshotService;
    let AAUMemberService;
    let SEMembersService;
    let XLSXMemberService;
    let SEUtils;
    let XLSXUtils;
    let masterClub;
    let clubOwner;
    let season;

    before(async () => {
        season = sails.config.sw_season.current;
        masterClub = ClubPreparationHelper.masterClubFixture.master_club_id;
        clubOwner = ClubPreparationHelper.clubOwnerFixture.club_owner_id;

        AAUMemberService = sails.services.aaumemberservice;
        AAUImportService = AAUMemberService.import;
        AAUService = sails.services.aauservice;

        SEMembersService = sails.services.sportenginememberservice;
        SEUtils = SEMembersService.utils;
        SEImportService = SEMembersService.import;
        SportsEngineService = sails.services.sportsengineservice;

        XLSXMemberService = sails.services.xlsxmemberservice;
        XLSXUtils = XLSXMemberService.utils;
        XLSXImportService = XLSXMemberService.import;

        RosterSnapshotService = sails.services.rostersnapshotservice;
        RosterSnapshotServiceClubSnapshotStub = sinon.stub(RosterSnapshotService, 'clubSnapshot').resolves({});

        await ClubPreparationHelper.addClubData();
    });

    beforeEach(async () => {
        AAUServiceGetMembersStub = sinon.stub(
            AAUService,
            'getMembers'
        ).resolves(AAUImportHelper.importResponse);

        SEServiceGetMembersStub = sinon.stub(
            SEImportService.process,
            'getClubMembersFromSportEngineAPI'
        ).resolves(SEImportHelper.importResponse);
    });

    after(async () => {
        await RosterSnapshotServiceClubSnapshotStub.restore();
        await ClubPreparationHelper.clearClubData();
        await XlsxImportHelper.clearUploadFolder(XLSXImportService.UPLOAD_DIR);
    })

    afterEach(async () => {
        AAUServiceGetMembersStub.restore();
        SEServiceGetMembersStub.restore();
    });

    context('Import', () => {
        afterEach(async () => {
            await ClubPreparationHelper.clearMembersData();
        })

        it('should successfully import members into empty club', async () => {
            const XLSX_FILE_NAME= 'SportWrench Foreign Team Roster Import.xlsx';

            const XLSXFilePath = await XlsxImportHelper.runImport(
                XLSXImportService,
                XLSX_FILE_NAME,
                masterClub,
                clubOwner,
                season
            );

            //Get members from DB
            const clubAthletes = await ClubPreparationHelper.getClubAthletes();
            const clubStaffers = await ClubPreparationHelper.getClubStaffers();

            /*
            * Check XLSX Data
            * - check athletes
            *   - check athletes counts
            *   - check athletes XLSX data
            * - check staffers
            *   - check staffers counts
            *   - check staffers XLSX data
            * */
            const membersFromFile = await XlsxImportHelper.getImportData(XLSXFilePath);
            const XlsxResultsChecker= new XlsxResultsHelper(XLSXUtils);
            await XlsxResultsChecker.checkMembers(membersFromFile, clubAthletes, clubStaffers);
        })

        it('should successfully import members into club when there are members imported from XLSX file', async () => {
            const XLSX_FILE_NAME= 'SportWrench Foreign Team Roster Import.xlsx';

            //First XLSX roster import
            const XLSXFilePath = await XlsxImportHelper.runImport(
                XLSXImportService,
                XLSX_FILE_NAME,
                masterClub,
                clubOwner,
                season
            );

            //Second XLSX roster import
            await XlsxImportHelper.runImport(
                XLSXImportService,
                XLSX_FILE_NAME,
                masterClub,
                clubOwner,
                season
            );

            //Get members from DB
            const clubAthletes = await ClubPreparationHelper.getClubAthletes();
            const clubStaffers = await ClubPreparationHelper.getClubStaffers();

            /*
            * Check XLSX Data
            * - check athletes
            *   - check athletes counts
            *   - check athletes XLSX data
            * - check staffers
            *   - check staffers counts
            *   - check staffers XLSX data
            * */
            const membersFromFile = await XlsxImportHelper.getImportData(XLSXFilePath);
            const XlsxResultsChecker= new XlsxResultsHelper(XLSXUtils);
            await XlsxResultsChecker.checkMembers(membersFromFile, clubAthletes, clubStaffers);
        })

        it('should successfully import members into club after import from AAU', async () => {
            //Prepare and run AAU import with insert mode
            await AAUImportHelper.prepareImportInsertMode();
            await AAUImportService.process.run();

            // Prepare stream for XLSX import after AAU import
            const XLSX_FILE_NAME= 'SportWrench Foreign Team Roster Import (for AAU).xlsx';

            const XLSXFilePath = await XlsxImportHelper.runImport(
                XLSXImportService,
                XLSX_FILE_NAME,
                masterClub,
                clubOwner,
                season
            );

            //Get members from DB
            const clubAthletes = await ClubPreparationHelper.getClubAthletes();
            const clubStaffers = await ClubPreparationHelper.getClubStaffers();

            /*
            * Check XLSX Data
            * - check athletes
            *   - check athletes counts
            *   - check athletes XLSX data
            * - check staffers
            *   - check staffers counts
            *   - check staffers XLSX data
            * */
            const membersFromFile = await XlsxImportHelper.getImportData(XLSXFilePath);
            const XlsxResultsChecker= new XlsxResultsHelper(XLSXUtils);
            await XlsxResultsChecker.checkMembers(membersFromFile, clubAthletes, clubStaffers);

            /*
            * Check AAU Data
            * - check members counts
            *   - AAU athletes from DB count should be equal to AAU athletes from Import
            *   - AAU staffers from DB count should be equal to AAU staffers from Import
            * - check athletes AAU data
            * - check staffers AAU data
            * */
            const AAUResultsChecker= new AAUResultsHelper(AAUUtils);
            const membersFromImport = AAUImportHelper.importResponse;
            AAUResultsChecker.checkMembersCounts(membersFromImport, clubAthletes, clubStaffers)
            AAUResultsChecker.checkAthletes(membersFromImport, clubAthletes);
            AAUResultsChecker.checkStaffers(membersFromImport, clubStaffers);
        })

        it('should successfully import members into club after import from USAV/SE', async () => {
            //Prepare and run USAV import with insert mode
            await SEImportHelper.prepareImportInsertMode();
            await SEMembersService.import.process.run();

            // Prepare stream for XLSX import after USAV import
            const XLSX_FILE_NAME= 'SportWrench Foreign Team Roster Import (for USAV).xlsx';

            const XLSXFilePath = await XlsxImportHelper.runImport(
                XLSXImportService,
                XLSX_FILE_NAME,
                masterClub,
                clubOwner,
                season
            );

            //Get members from DB
            const clubAthletes = await ClubPreparationHelper.getClubAthletes();
            const clubStaffers = await ClubPreparationHelper.getClubStaffers();

            /*
            * Check XLSX Data
            * - check athletes
            *   - check athletes counts
            *   - check athletes XLSX data
            * - check staffers
            *   - check staffers counts
            *   - check staffers XLSX data
            * */
            const membersFromFile = await XlsxImportHelper.getImportData(XLSXFilePath);
            const XlsxResultsChecker= new XlsxResultsHelper(XLSXUtils);
            await XlsxResultsChecker.checkMembers(membersFromFile, clubAthletes, clubStaffers);

            /*
            * Check SE Data
            * - check members counts
            *   - SE athletes from DB count should be equal to SE athletes from Import
            *   - SE staffers from DB count should be equal to SE staffers from Import
            * - check athletes SE data
            * - check staffers SE data
            * */
            const SEResultsChecker= new SEResultsHelper(SEUtils);
            const membersFromSEImport = SEImportHelper.importResponse;
            const { groupedMembers: groupedMembersFromSEAPI }
                = await SEImportService.process.memberImport.groupMembers(membersFromSEImport);

            SEResultsChecker.checkMembersCounts(groupedMembersFromSEAPI, clubAthletes, clubStaffers);
            SEResultsChecker.checkAthletes(membersFromSEImport, clubAthletes);
            SEResultsChecker.checkStaffers(membersFromSEImport, clubStaffers);
        })
    })
})
