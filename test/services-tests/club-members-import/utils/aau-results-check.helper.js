
const moment = require('moment');

class AauResultsCheckHelper {
    constructor(AAUUtils) {
        this.AAUUtils = AAUUtils;
    }

    checkMembersCounts(membersFromImport, clubAthletes, clubStaffers) {
        const staffersInResponse = this.#extractStaffersFromImportData(membersFromImport);
        const athletesInResponse = this.#extractAthletesFromImportData(membersFromImport);

        const clubAAUAthletes = this.#extractAAUMembers(clubAthletes);
        const clubAAUStaffers = this.#extractAAUMembers(clubStaffers);

        this.#checkEquality(clubAAUAthletes.length, athletesInResponse.length);
        this.#checkEquality(clubAAUStaffers.length, staffersInResponse.length);
    }

    checkStaffers(membersFromAPI, membersFromDB) {
        this.#checkMembersData(membersFromAPI, membersFromDB, 'staff');
    }

    checkAthletes(membersFromAPI, membersFromDB) {
        this.#checkMembersData(membersFromAPI, membersFromDB, 'athlete');
    }

    #checkMembersData(membersFromAPI, membersFromDB, memberType) {
        for(const memberFromDB of membersFromDB) {
            const [memberFromAPI] = this.#findMemberFromAPI(membersFromAPI, memberFromDB);

            if(!_.isEmpty(memberFromAPI)) {
                this.#checkMemberData(memberType, memberFromAPI, memberFromDB);
            }
        }
    }

    #findMemberFromAPI(membersFromAPI, memberFromDB) {
        const F = this.AAUUtils.SOAP_MEMBER_FIELDS || {};
        return membersFromAPI.filter(
            member => (member.membershipNumber || member[F.AAU_MEMBERSHIP_ID]) === memberFromDB.aau_membership_id
        );
    }

    #checkMemberData(memberType, memberFromAPI, memberFromDB) {
        const F = this.AAUUtils.SOAP_MEMBER_FIELDS || {};
        const first = memberFromAPI.firstName || memberFromAPI[F.FIRST] || '';
        const last = memberFromAPI.lastName || memberFromAPI[F.LAST] || '';
        const birthDate = memberFromAPI.birthDate || memberFromAPI[F.BIRTHDATE];
        const genderApp = (memberFromAPI.gender
            ? ((this.AAUUtils.mapCanonicalGenderToApp && this.AAUUtils.mapCanonicalGenderToApp(memberFromAPI.gender))
                || (memberFromAPI.gender === 'MALE' ? 'male' : memberFromAPI.gender === 'FEMALE' ? 'female' : 'non-binary'))
            : this.AAUUtils.GENDER && this.AAUUtils.GENDER[memberFromAPI[F.GENDER]]);
        const membershipNumber = memberFromAPI.membershipNumber || memberFromAPI[F.AAU_MEMBERSHIP_ID];
        const endYear = memberFromAPI.endYear || memberFromAPI[F.AAU_MEMBERSHIP_ENDING_YEAR];
        const country = memberFromAPI.countryCode || memberFromAPI[F.COUNTRY];

        this.#checkEquality(memberFromDB.first.trim().toLowerCase(), (first || '').trim().toLowerCase());
        this.#checkEquality(memberFromDB.last.trim().toLowerCase(), (last || '').trim().toLowerCase());
        this.#checkEquality(
            moment(memberFromDB.birthdate).format('YYYY-MM-DD'),
            moment(birthDate).format('YYYY-MM-DD')
        );
        this.#checkEquality(memberFromDB.gender, genderApp);
        this.#checkEquality(memberFromDB.season, sails.config.sw_season.current);
        this.#checkEquality(memberFromDB.aau_membership_id, membershipNumber);
        this.#checkEquality(
            memberFromDB.aau_membership_ending_year,
            endYear
        );

        expect(memberFromDB.aau_sync).to.be.not.null;

        if(memberType === 'athlete') {
            this.#checkEquality(memberFromDB.age, this.AAUUtils.getMinAge(birthDate));
            this.#checkEquality(memberFromDB.country, country);
        }
    }

    #checkEquality(actualValue, expectedValue) {
        expect(actualValue).to.be.equal(expectedValue);
    }

    #extractAAUMembers(members) {
        return members.filter(member => !!member.aau_membership_id);
    }

    #extractStaffersFromImportData(membersFromImport) {
        return this.#extractMembersFromImportData(membersFromImport, this.AAUUtils.MEMBER_TYPE.STAFF);

    }

    #extractAthletesFromImportData(membersFromImport) {
        return this.#extractMembersFromImportData(membersFromImport, this.AAUUtils.MEMBER_TYPE.ATHLETE);
    }

    #extractMembersFromImportData(membersFromImport, extractingMemberType) {
        return membersFromImport.filter(
            member => {
                const F = this.AAUUtils.SOAP_MEMBER_FIELDS || {};
                const typeCanonical = member.membershipType
                    ? (member.membershipType === 'ATHLETE' ? this.AAUUtils.MEMBER_TYPE.ATHLETE : this.AAUUtils.MEMBER_TYPE.STAFF)
                    : undefined;
                const typeSoap = F.CATEGORY_CODE ? this.AAUUtils.CATEGORY_CODE[member[F.CATEGORY_CODE]] : undefined;
                const memberType = typeCanonical || typeSoap;
                return memberType === extractingMemberType;
            }
        );
    }
}

module.exports = AauResultsCheckHelper;
