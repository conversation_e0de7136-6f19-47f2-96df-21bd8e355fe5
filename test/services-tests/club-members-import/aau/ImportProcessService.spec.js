
const AAUUtils = require('../../../../api/lib/AAUUtilsService');

const AAUImportHelper = require('../utils/aau-import.helper');
const SEImportHelper = require('../utils/se-import.helper');
const ClubPreparationHelper = require('../utils/club-preparation.helper');
const AAUResultsHelper = require('../utils/aau-results-check.helper');
const SEResultsHelper = require('../utils/se-results-check.helper');

/*
* Testing flows
* 1. Import from AAU when there are no members in club.
* 2. Import from AAU when there are members imported from XLSX file.
* 3. Import from AAU after import from AAU
* 4. Import from AAU after import from USAV/SE
*/


describe('AAU Club Members Import', () => {
    let AAUImportService;
    let SEImportService;
    let RosterSnapshotServiceClubSnapshotStub;
    let AAUServiceGetMembersStub;
    let SEServiceGetMembersStub;
    let AAUService;
    let SportsEngineService;
    let RosterSnapshotService;
    let AAUMemberService;
    let SEUtils;
    let SEMembersService;


    before(async () => {
        AAUMemberService = sails.services.aaumemberservice;
        AAUImportService = AAUMemberService.import;
        SEMembersService = sails.services.sportenginememberservice;
        SEImportService = SEMembersService.import;
        AAUService = AAUMemberService.aauSoap;
        SportsEngineService = sails.services.sportsengineservice;
        RosterSnapshotService = sails.services.rostersnapshotservice;
        SEUtils = SEMembersService.utils;

        RosterSnapshotServiceClubSnapshotStub = sinon.stub(RosterSnapshotService, 'clubSnapshot').resolves({});

        await ClubPreparationHelper.addClubData();
    });

    beforeEach(async () => {
        AAUServiceGetMembersStub = sinon.stub(
            AAUService,
            'getMembersCanonical'
        ).resolves(AAUImportHelper.importResponse.map(m => AAUUtils.mapSoapDtoToCanonical(m)));

        SEServiceGetMembersStub = sinon.stub(
            SEImportService.process,
            'getClubMembersFromSportEngineAPI'
        ).resolves(SEImportHelper.importResponse);
    });

    after(async () => {
        await RosterSnapshotServiceClubSnapshotStub.restore();
        await ClubPreparationHelper.clearClubData();
    })

    afterEach(async () => {
        AAUServiceGetMembersStub.restore();
        SEServiceGetMembersStub.restore();

        await AAUImportHelper.clearQueue();
    });

    context('Insert Mode', () => {
        beforeEach(async () => {
            await AAUImportHelper.prepareImportInsertMode();
        })

        afterEach(async () => {
            await AAUImportHelper.clearQueue();
            await ClubPreparationHelper.clearMembersData();
        })

        it('should successfully import members into empty club', async () => {
            //Run AAU Import
            await AAUImportService.process.run();

            //Get members from DB
            const clubAthletes = await ClubPreparationHelper.getClubAthletes();
            const clubStaffers = await ClubPreparationHelper.getClubStaffers();

            /*
            * Check AAU Data
            * - check members counts
            *   - AAU athletes from DB count should be equal to AAU athletes from Import
            *   - AAU staffers from DB count should be equal to AAU staffers from Import
            * - check staffers AAU data
            * - check athletes AAU data
            * */
            const AAUResultsChecker = new AAUResultsHelper(AAUUtils);
            const membersFromImport = AAUImportHelper.importResponse;
            AAUResultsChecker.checkMembersCounts(membersFromImport, clubAthletes, clubStaffers);
            AAUResultsChecker.checkAthletes(membersFromImport, clubAthletes);
            AAUResultsChecker.checkStaffers(membersFromImport, clubStaffers);
        })

        it('should successfully import members into empty club when there are members imported from XLSX file', async () => {
            //Add members to DB like it could be done using XLSX import
            await ClubPreparationHelper.addMembersData();

            //Run AAU Import
            await AAUImportService.process.run();

            //Get members from DB
            const clubAthletes = await ClubPreparationHelper.getClubAthletes();
            const clubStaffers = await ClubPreparationHelper.getClubStaffers();

            /*
            * Check AAU Data
            * - check members counts
            *   - AAU athletes from DB count should be equal to AAU athletes from Import
            *   - AAU staffers from DB count should be equal to AAU staffers from Import
            * - check staffers AAU data
            * - check athletes AAU data
            * */
            const AAUResultsChecker = new AAUResultsHelper(AAUUtils);
            const membersFromImport = AAUImportHelper.importResponse;
            AAUResultsChecker.checkMembersCounts(membersFromImport, clubAthletes, clubStaffers);
            AAUResultsChecker.checkAthletes(membersFromImport, clubAthletes);
            AAUResultsChecker.checkStaffers(membersFromImport, clubStaffers);
        })

        it('should successfully import members into empty club after import from AAU', async () => {
            //Run AAU Import
            await AAUImportService.process.run();

            //Prepare and run new AAU import with insert mode
            await AAUImportHelper.prepareImportInsertMode();
            await AAUImportService.process.run();

            //Get members from DB
            const clubAthletes = await ClubPreparationHelper.getClubAthletes();
            const clubStaffers = await ClubPreparationHelper.getClubStaffers();

            /*
            * Check AAU Data
            * - check members counts
            *   - AAU athletes from DB count should be equal to AAU athletes from Import
            *   - AAU staffers from DB count should be equal to AAU staffers from Import
            * - check staffers AAU data
            * - check athletes AAU data
            * */
            const AAUResultsChecker = new AAUResultsHelper(AAUUtils);
            const membersFromImport = AAUImportHelper.importResponse;
            AAUResultsChecker.checkMembersCounts(membersFromImport, clubAthletes, clubStaffers)
            AAUResultsChecker.checkAthletes(membersFromImport, clubAthletes);
            AAUResultsChecker.checkStaffers(membersFromImport, clubStaffers);
        })

        it('should successfully import members into empty club after import from USAV/SE', async () => {
            //Add SE queue record to DB
            await SEImportHelper.prepareImportInsertMode();

            //Run SE Import
            await SEMembersService.import.process.run();
            //Run AAU Import
            await AAUImportService.process.run();

            //Get members from DB
            const clubAthletes = await ClubPreparationHelper.getClubAthletes();
            const clubStaffers = await ClubPreparationHelper.getClubStaffers();

            /*
            * Check AAU Data
            * - check members counts
            *   - AAU athletes from DB count should be equal to AAU athletes from Import
            *   - AAU staffers from DB count should be equal to AAU staffers from Import
            * - check staffers AAU data
            * - check athletes AAU data
            * */
            const AAUResultsChecker = new AAUResultsHelper(AAUUtils);
            const membersFromAAUImport = AAUImportHelper.importResponse;
            AAUResultsChecker.checkMembersCounts(membersFromAAUImport, clubAthletes, clubStaffers);
            AAUResultsChecker.checkAthletes(membersFromAAUImport, clubAthletes);
            AAUResultsChecker.checkStaffers(membersFromAAUImport, clubStaffers);

            /*
            * Check SE Data
            * - check members counts
            *   - SE athletes from DB count should be equal to SE athletes from Import
            *   - SE staffers from DB count should be equal to SE staffers from Import
            * - check staffers SE data
            * - check athletes SE data
            * */
            const SEResultsChecker = new SEResultsHelper(SEUtils);
            const membersFromSEImport = SEImportHelper.importResponse;
            const { groupedMembers: groupedMembersFromSEAPI }
                = await SEImportService.process.memberImport.groupMembers(membersFromSEImport);

            SEResultsChecker.checkMembersCounts(groupedMembersFromSEAPI, clubAthletes, clubStaffers);
            SEResultsChecker.checkAthletes(membersFromSEImport, clubAthletes);
            SEResultsChecker.checkStaffers(membersFromSEImport, clubStaffers);
        })
    })

    context('Update Mode', () => {
        beforeEach(async () => {
            await AAUImportHelper.prepareImportUpdateMode();
        })

        afterEach(async () => {
            await AAUImportHelper.clearQueue();
            await ClubPreparationHelper.clearMembersData();
        })

        it('should successfully import members that not exist in db', async () => {
            //Run AAU Import
            await AAUImportService.process.run();

            //Get members from DB
            const clubAthletes = await ClubPreparationHelper.getClubAthletes();
            const clubStaffers = await ClubPreparationHelper.getClubStaffers();

            /*
            * Check AAU Data
            * - check members counts
            *   - AAU athletes from DB count should be equal to AAU athletes from Import
            *   - AAU staffers from DB count should be equal to AAU staffers from Import
            * - check staffers AAU data
            * - check athletes AAU data
            * */
            const AAUResultsChecker = new AAUResultsHelper(AAUUtils);
            const membersFromImport = AAUImportHelper.importResponse;
            AAUResultsChecker.checkMembersCounts(membersFromImport, clubAthletes, clubStaffers);
            AAUResultsChecker.checkAthletes(membersFromImport, clubAthletes);
            AAUResultsChecker.checkStaffers(membersFromImport, clubStaffers);
        })

        it('should successfully import members into empty club when there are members imported from XLSX file', async () => {
            //Add members to DB like it could be done using XLSX import
            await ClubPreparationHelper.addMembersData();

            //Run AAU Import
            await AAUImportService.process.run();

            //Get members from DB
            const clubAthletes = await ClubPreparationHelper.getClubAthletes();
            const clubStaffers = await ClubPreparationHelper.getClubStaffers();

            /*
            * Check AAU Data
            * - check members counts
            *   - AAU athletes from DB count should be equal to AAU athletes from Import
            *   - AAU staffers from DB count should be equal to AAU staffers from Import
            * - check staffers AAU data
            * - check athletes AAU data
            * */
            const AAUResultsChecker = new AAUResultsHelper(AAUUtils);
            const membersFromImport = AAUImportHelper.importResponse;
            AAUResultsChecker.checkMembersCounts(membersFromImport, clubAthletes, clubStaffers);
            AAUResultsChecker.checkAthletes(membersFromImport, clubAthletes);
            AAUResultsChecker.checkStaffers(membersFromImport, clubStaffers);
        })

        it('should successfully import members into empty club after import from AAU', async () => {
            //Run AAU Import
            await AAUImportService.process.run();

            //Prepare and run new AAU import with insert mode
            await AAUImportHelper.prepareImportUpdateMode();
            await AAUImportService.process.run();

            //Get members from DB
            const clubAthletes = await ClubPreparationHelper.getClubAthletes();
            const clubStaffers = await ClubPreparationHelper.getClubStaffers();

            /*
            * Check AAU Data
            * - check members counts
            *   - AAU athletes from DB count should be equal to AAU athletes from Import
            *   - AAU staffers from DB count should be equal to AAU staffers from Import
            * - check staffers AAU data
            * - check athletes AAU data
            * */
            const AAUResultsChecker = new AAUResultsHelper(AAUUtils);
            const membersFromImport = AAUImportHelper.importResponse;
            AAUResultsChecker.checkMembersCounts(membersFromImport, clubAthletes, clubStaffers)
            AAUResultsChecker.checkAthletes(membersFromImport, clubAthletes);
            AAUResultsChecker.checkStaffers(membersFromImport, clubStaffers);
        })

        it('should successfully import members into empty club after import from USAV/SE', async () => {
            //Add SE queue record to DB
            await SEImportHelper.prepareImportInsertMode();

            //Run SE Import
            await SEMembersService.import.process.run();
            //Run AAU Import
            await AAUImportService.process.run();

            //Get members from DB
            const clubAthletes = await ClubPreparationHelper.getClubAthletes();
            const clubStaffers = await ClubPreparationHelper.getClubStaffers();

            /*
            * Check AAU Data
            * - check members counts
            *   - AAU athletes from DB count should be equal to AAU athletes from Import
            *   - AAU staffers from DB count should be equal to AAU staffers from Import
            * - check staffers AAU data
            * - check athletes AAU data
            * */
            const AAUResultsChecker = new AAUResultsHelper(AAUUtils);
            const membersFromAAUImport = AAUImportHelper.importResponse;
            AAUResultsChecker.checkMembersCounts(membersFromAAUImport, clubAthletes, clubStaffers);
            AAUResultsChecker.checkAthletes(membersFromAAUImport, clubAthletes);
            AAUResultsChecker.checkStaffers(membersFromAAUImport, clubStaffers);

            /*
            * Check SE Data
            * - check members counts
            *   - SE athletes from DB count should be equal to SE athletes from Import
            *   - SE staffers from DB count should be equal to SE staffers from Import
            * - check staffers SE data
            * - check athletes SE data
            * */
            const SEResultsChecker = new SEResultsHelper(SEUtils);
            const membersFromSEImport = SEImportHelper.importResponse;
            const { groupedMembers: groupedMembersFromSEAPI }
                = await SEImportService.process.memberImport.groupMembers(membersFromSEImport);

            SEResultsChecker.checkMembersCounts(groupedMembersFromSEAPI, clubAthletes, clubStaffers);
            SEResultsChecker.checkAthletes(membersFromSEImport, clubAthletes);
            SEResultsChecker.checkStaffers(membersFromSEImport, clubStaffers);
        })
    })
})
