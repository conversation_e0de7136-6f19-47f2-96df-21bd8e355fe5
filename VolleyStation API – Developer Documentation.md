# VolleyStation API – Developer Documentation

This document describes the VolleyStation API surface implemented in SportWrench. It covers endpoints, authentication, pagination, rate limiting, and error handling, with examples for both fetching data and posting match results.

## Base URL

- All endpoints are prefixed with: `/api/volley-station/v1`

Examples:
- `/api/volley-station/v1/events/:eventId/schedule`
- `/api/volley-station/v1/events/:eventId/team-roster`
- `/api/volley-station/v1/events/:eventId/results`

Notes:
- `eventId` is a required numeric path parameter.

## Authentication

- Scheme: Token via HTTP `Authorization` header
- Header: `Authorization: <YOUR_API_KEY>`
- Tokens are provisioned out-of-band. Request an API key from the SportWrench team and keep it secure.

Auth failures (policy-enforced):
- `401 Unauthorized` if the `Authorization` header is missing or invalid.
  - `{ "message": "Authorization token is missing" }`
  - `{ "message": "Authorization token invalid" }`

## Event Access Control

- All VolleyStation endpoints are restricted to events with VolleyStation integration enabled.
- If the event is not enabled, the API returns:
  - `403 Forbidden` with body: `{ "message": "Event not available for VolleyStation integration" }`

## Rate Limiting

- Read endpoints (GET) are rate-limited per authorized client.
- Write endpoints (POST) are also rate-limited per authorized client.
- Behavior:
  - On limit exceeded, the API returns `429 Too Many Requests` (includes retry hint headers when applicable).
- Common headers:
  - `RateLimit-Limit`
  - `RateLimit-Remaining`
  - `RateLimit-Reset`
  - `Retry-After` (on `429`)

Note: Specific configuration details (points/duration) are internal and may change.

## Pagination

All list endpoints support pagination.

- Query parameters:
  - `page`: number (default `1`, min `1`)
  - `limit`: number (default `50`, min `1`, max `100`)
- Response payload includes a `pagination` object:
  - `pagination.page`: current page number
  - `pagination.limit`: requested page size
  - `pagination.total`: total items across all pages
  - `pagination.totalPages`: computed total pages
  - `pagination.hasNext`: whether there is a next page
  - `pagination.hasPrev`: whether there is a previous page

Mechanics:
- Use `page` and `limit` in query string to navigate.
- When `pagination.hasNext` is true, increment `page` for the next request.

## Endpoints

### 1) Get Event Schedule

- Method: `GET`
- Path: `/api/volley-station/v1/events/:eventId/schedule`
- Description: Returns paginated schedule (matches) for the event. Includes match results and assigned official info when available.
- Path params:
  - `eventId` (number, required): ID of the event
- Query params:
  - `page` (number, default `1`)
  - `limit` (number, default `50`, max `100`)
- Auth: Required (`Authorization` header)
- Rate limiting: Read policy

Response 200 (shape):
- `data`: array of match objects:
  - `gender`: string
  - `division_name`: string
  - `division_id`: number
  - `match_uuid`: string (UUID)
  - `event`: number (event ID)
  - `match_id`: string (composite match display ID)
  - `div`: string (short division)
  - `day`: number
  - `date_time`: string (ISO 8601 in UTC, e.g., `2025-03-28T12:00:00Z`)
  - `court`: number (sort priority)
  - `court_alpha`: string (alphanumeric court name)
  - `pool`: string
  - `team1_roster_id`: number | null
  - `team2_roster_id`: number | null
  - `ref_roster_id`: number | null
  - `team_1_name`: string | null
  - `team_2_name`: string | null
  - `ref_name`: string | null
  - `master_team_id_1`: number | null
  - `master_team_id_2`: number | null
  - `match_type`: string
  - `results`: object with set scores (only set keys present will be returned), e.g. `{ set1: "27-25", set2: "25-13" }`
  - `official`: string | null (assigned official display name)
  - `official_id`: number | null (assigned event official ID)
- `pagination`: pagination object (see above)

Errors:
- `400 Bad Request` if `eventId` is not a valid positive integer
  - `{ "message": "Invalid event identifier" }`
- `401 Unauthorized` missing/invalid token (see Authentication)
- `403 Forbidden` if event is not enabled for VolleyStation
  - `{ "message": "Event not available for VolleyStation integration" }`
- `429 Too Many Requests` if rate-limited
- `500 Internal Server Error` on unexpected errors

Example:
```
GET /api/volley-station/v1/events/25184/schedule?page=1&limit=10
Authorization: YOUR_API_KEY
```

### 2) Get Team Roster

- Method: `GET`
- Path: `/api/volley-station/v1/events/:eventId/team-roster`
- Description: Returns paginated roster data (teams with athletes and staff) for the event.
- Path params:
  - `eventId` (number, required): ID of the event
- Query params:
  - `page` (number, default `1`)
  - `limit` (number, default `50`, max `100`)
- Auth: Required (`Authorization` header)
- Rate limiting: Read policy

Response 200 (shape):
- `data`: array of team roster entries:
  - `division_id`: number
  - `division_name`: string
  - `team_id`: number
  - `team_name`: string
  - `club_name`: string
  - `athletes`: array of `{ athlete_id, first, last, position, jersey }`
  - `staff`: array of `{ first, last, role_name }`
- `pagination`: pagination object (see above)

Errors: same as Schedule.

Example:
```
GET /api/volley-station/v1/events/22343/team-roster?page=2&limit=50
Authorization: YOUR_API_KEY
```

### 3) Submit Match Results

- Method: `POST`
- Path: `/api/volley-station/v1/events/:eventId/results`
- Description: Submit final match results to the event journal system.
- Path params:
  - `eventId` (number, required): ID of the event
- Body (JSON):
  - `match_uuid` (string, required): UUID of the match
  - `is_final` (boolean, required): must be `true`
  - `finished_at` (string, optional): ISO 8601 UTC timestamp when match finished
  - `official_id` (number, optional): Event official ID submitting the results; must be assigned to the match to be authorized
  - `winner` (number, required): `1` (team 1) or `2` (team 2)
  - `results` (object, required): Set scores with keys `set1`..`set5` (strings in format `XX-YY`); `set1` is required

Validation rules (enforced by action):
- `match_uuid` must be a valid UUID string
- `is_final` must be `true`
- `finished_at` (if present) must be a valid ISO 8601 timestamp
- `official_id` (if present) must be a positive integer and the official must be authorized for the match
- `winner` must be `1` or `2`
- `results.set1` is required, each provided set must match `^\d{1,2}-\d{1,2}$`

Auth and rate limiting:
- Auth: Required (`Authorization` header)
- Rate limiting: Write policy

Successful response 200 (shape):
- `{ "id": "<journalId>", "is_live": true|false }`
  - `is_live=false` indicates results were accepted but not published to the live system (event’s live publishing disabled)

Error responses:
- `401 Unauthorized` missing/invalid token (see Authentication)
- `403 Forbidden` if score entry is not allowed for the event, or official is not authorized for the match
  - `{ "message": "Score entry not allowed for this event" }`
  - `{ "message": "Official not authorized to submit results for this match" }`
- `404 Not Found` if event or match is not found, or mismatch between event and match
  - `{ "message": "Event not found" }`
  - `{ "message": "Match not found" }`
- `429 Too Many Requests` if rate-limited
- `500 Internal Server Error` on unexpected errors

Example request:
```
POST /api/volley-station/v1/events/25184/results
Authorization: YOUR_API_KEY
Content-Type: application/json

{
  "match_uuid": "7a88aa13-9157-4064-927b-81cb49fb8e99",
  "is_final": true,
  "finished_at": "2025-03-28T13:05:00Z",
  "official_id": 12345,
  "winner": 1,
  "results": {
    "set1": "27-25",
    "set2": "25-13"
  }
}
```

## Status Codes

- `200 OK`: Successful response with data (or result submission status)
- `400 Bad Request`: Invalid path/query/body parameter
- `401 Unauthorized`: Missing or invalid `Authorization` token
- `403 Forbidden`: Event not enabled for VolleyStation or action not permitted
- `404 Not Found`: Event or match not found
- `429 Too Many Requests`: Rate limit exceeded (includes rate limit headers / retry hint)
- `500 Internal Server Error`: Unexpected server or access verification failure

## Error Payload Format

Unless otherwise noted, error responses use this structure:
- `{ "message": "<short description>" }`

Examples:
- `{ "message": "Authorization token is missing" }`
- `{ "message": "Authorization token invalid" }`
- `{ "message": "Invalid event identifier" }`
- `{ "message": "Event not available for VolleyStation integration" }`
- `{ "message": "Server Internal Error" }`

## Request Examples (curl)

Fetch schedule:
```
curl -sS \
  -H "Authorization: YOUR_API_KEY" \
  "https://your-host/api/volley-station/v1/events/25184/schedule?page=1&limit=10"
```

Fetch team roster:
```
curl -sS \
  -H "Authorization: YOUR_API_KEY" \
  "https://your-host/api/volley-station/v1/events/22343/team-roster?page=2&limit=50"
```

Submit match results:
```
curl -sS -X POST \
  -H "Authorization: YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "match_uuid": "7a88aa13-9157-4064-927b-81cb49fb8e99",
    "is_final": true,
    "finished_at": "2025-03-28T13:05:00Z",
    "official_id": 12345,
    "winner": 1,
    "results": { "set1": "27-25", "set2": "25-13" }
  }' \
  "https://your-host/api/volley-station/v1/events/25184/results"
```
