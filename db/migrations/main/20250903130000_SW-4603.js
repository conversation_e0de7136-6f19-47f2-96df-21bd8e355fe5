exports.up = function(knex) {
    return knex.schema.raw(`
        -- Add new teams_tab sub-permissions
        INSERT INTO event_operation (event_operation, parent_event_operation, title)
            VALUES ('teams_list_tab', 'teams_tab', 'Teams List'),
                   ('teams_payments_tab', 'teams_tab', 'Payments'),
                   ('teams_club_invoices_tab', 'teams_tab', 'Club Invoices')
        ON CONFLICT (event_operation) DO NOTHING;
        
        -- Grant all existing Co-EOs with teams_tab permission the new sub-permissions
        INSERT INTO event_user_permission (event_operation_id, event_id, user_id, granter_user_id)
        SELECT eoi.event_operation_id, eup.event_id, eup.user_id, eup.granter_user_id
        FROM event_user_permission eup
        CROSS JOIN (VALUES ('teams_list_tab'), ('teams_payments_tab'), ('teams_club_invoices_tab')) eoi(event_operation_id)
        WHERE eup.event_operation_id = 'teams_tab' AND eup.deleted IS NULL
        ON CONFLICT (event_operation_id, event_id, user_id) DO NOTHING;
    `);
};

exports.down = function(knex) {
    return knex.schema.raw(`
        -- Remove permissions for the sub-permissions
        DELETE FROM event_operation WHERE event_operation IN ('teams_list_tab', 'teams_payments_tab', 'teams_club_invoices_tab');
        -- Remove user permissions for the sub-permissions
        DELETE FROM event_user_permission WHERE event_operation_id IN ('teams_list_tab', 'teams_payments_tab', 'teams_club_invoices_tab');
    `);
};
