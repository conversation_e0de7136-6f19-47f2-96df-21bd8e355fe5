/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE "event_email" 
            ADD COLUMN IF NOT EXISTS "event_custom_form_id" INTEGER NULL,
            ADD COLUMN IF NOT EXISTS "event_custom_form_submitter_id" INTEGER NULL;
    `);
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
    return knex.schema.raw(`
        ALTER TABLE "event_email"
            DROP COLUMN IF EXISTS "event_custom_form_id",
            DROP COLUMN IF EXISTS "event_custom_form_submitter_id";
    `);
};
